from dash import dcc, html, dash_table # Import necessary components

layout = html.Div([
    html.H1("Detect Peaks from Chromatogram", className="app-title"),

    # File Upload Section
    html.Div([
        html.Div([
            # Sample file upload
            html.Div([
                html.Label("Load Sample Data"),
                html.Div([
                    dcc.Upload(
                        id='upload-sample-predict',
                        children=html.Div(['Select excel/csv/CH2/cdf... ']),
                        accept='.xlsx,.xls,.csv,.CH2,.cdf',
                        className="upload-box"
                    ),
                    html.Button(
                        "🗄️",
                        id='sample-cloud-btn-predict',
                        className="upload-icon-btn",
                        title="Upload from server"
                    )
                ], className="upload-with-download"),
                html.Div(id='upload-sample-filename-predict', className="filename-display")
            ], className="upload-item"),

            # Background file upload
            html.Div([
                html.Label("Load Background Data"),
                html.Div([
                    dcc.Upload(
                        id='upload-background-predict',
                        children=html.Div(['Select excel/csv/CH2/cdf... ']),
                        accept='.xlsx,.xls,.csv,.CH2,.cdf',
                        className="upload-box"
                    ),
                    html.Button(
                        "🗄️",
                        id='background-cloud-btn-predict',
                        className="upload-icon-btn",
                        title="Upload from server"
                    )
                ], className="upload-with-download"),
                html.Div(id='upload-background-filename-predict', className="filename-display")
            ], className="upload-item"),

            # Peak table upload
            html.Div([
                html.Label("Upload Peak Table (Optional)"),
                html.Div([
                    dcc.Upload(
                        id='upload-peaks-predict',
                        children=html.Div(['Select excel/csv... ']),
                        accept='.xlsx,.xls,.csv',
                        className="upload-box"
                    ),
                    html.Button(
                        "⬇️",
                        id='download-demo-peaks-btn-predict',
                        className="download-icon-btn",
                        title="Download demo file"
                    )
                ], className="upload-with-download"),
            ], className="upload-item"),
        ], className="upload-grid"),
    ], className="file-uploads", style={"maxWidth": "1320px"}),

    # Main Content (2-column layout)
    html.Div([
        # Left Column (Chromatogram and Peak Table)
        html.Div([
            # Chromatogram Panel
            html.Div([
                html.Div([
                    html.Button('Reset Zoom', id='reset-button-predict', className="action-button"), # New ID
                ], className="buttons"),
                dcc.Graph(
                    id='chromatogram-plot-predict', # New ID
                    className="plot-container",
                    config={
                        'displayModeBar': True,
                        'modeBarButtonsToAdd': ['select2d'],
                        'scrollZoom': True,
                        'doubleClick': 'reset',
                        'showAxisDragHandles': True,
                        'displaylogo': False,
                    }
                ),
                html.Div([
                    html.P("Click the 'Box Select' button in the toolbar, then drag to select an area to zoom in.",
                           style={'textAlign': 'center', 'color': '#666'})
                ]),
            ], className="panel"),

            # Peak Table Panel
            html.Div([
                html.Div([
                    html.Div("Peak Table", className="panel-title"),
                    html.Div([
                        html.Button('Add New Peak', id='add-row-button-predict', className="action-button"),
                        html.Button('Download Peak Table', id='download-button-predict', className="download-btn")
                    ], className="buttons"),
                ], className="peak-table-header"),
                html.Div(id='peaks-api-status-predict', className="status-message", style={"marginBottom": "0px", "marginTop": "0px"}),
                html.Div(id='peaks-upload-status-predict', className="status-message", style={"marginBottom": "0px", "marginTop": "0px"}),
                html.Div(id='peaks-table-container-predict', className="table-container")
            ], className="panel", style={"marginTop": "24px"}),
        ], className="main-column", style={"maxWidth": "1000px", "flex": "0 0 1000px"}),

        # Right Column (Integration Range, Long Baselines, Identify Peaks, Integration Parameters)
        html.Div([
            html.Div([
                # Panel for Integration Range and Long Baselines
                html.Div([
                    # Integration Range Section
                    html.Div("Integration Range", className="panel-title", style={"marginBottom": "8px"}),
                    html.Div([
                        html.Div([
                            html.Label("Start time (min):", style={"marginRight": "8px", "minWidth": "48px"}),
                            dcc.Input(id="integration-range-start-predict", type="number", value="", style={"width": "80px"}),
                        ], style={"display": "flex", "alignItems": "center", "marginBottom": "6px", "lineHeight": "2.4"}),
                        html.Div([
                            html.Label("End time (min):", style={"marginRight": "16px", "minWidth": "48px"}),
                            dcc.Input(id="integration-range-end-predict", type="number", value="", style={"width": "80px"}),
                        ], style={"display": "flex", "alignItems": "center", "marginBottom": "6px", "lineHeight": "2.4"}),
                        html.Hr(style={"border": "none", "borderTop": "1px solid #e0e0e0", "margin": "24px 0 16px 0"}),
                    ]),

                    # Minimum Area Setting Section
                    html.Div("Minimum Area Setting", className="panel-title", style={"marginBottom": "8px"}),
                    html.Div([
                        html.Div([
                            html.Label("Min area (%):", style={"marginRight": "24px", "minWidth": "48px"}),
                            dcc.Input(id="min-area-percent-predict", type="number", value=0.05, step=0.01, style={"width": "80px"}),
                        ], style={"display": "flex", "alignItems": "center", "marginBottom": "6px", "lineHeight": "2.4"}),
                        html.Hr(style={"border": "none", "borderTop": "1px solid #e0e0e0", "margin": "24px 0 16px 0"}),
                    ]),

                    # Long Baselines Section
                    html.Div("Long Baselines (min)", className="panel-title", style={"marginBottom": "8px"}),
                    html.Div(id="long-baselines-container-predict"),
                    html.Button("+ Add Baseline", id="add-baseline-predict", style={"marginLeft": "25%", "marginTop": "8px"}, className="action-button"),
                ], className="panel", style={"marginBottom": "0px"}),

                # Identify Peaks Button
                html.Button([html.Span("Identify Peaks")], id="identify-peaks-predict", className="identify-peaks-btn"),
                
                # Loading indicator for Identify Peaks
                html.Div(id='peaks-loading-predict', children=[
                    html.Div("Identifying peaks...", style={'textAlign': 'center', 'color': '#666', 'fontStyle': 'italic'})
                ], style={'display': 'none', 'marginTop': '10px'}),
                
                # Global Parameters Table
                html.Div([
                    html.Div([
                        html.Div("Global Parameters", className="panel-title"),
                        html.Div([
                            html.Button("📋", id='copy-global-params-btn-predict', className="icon-btn", title="Copy to clipboard", style={"border": "none", "background": "none", "cursor": "pointer", "fontSize": "16px"}),
                            html.Button("⬇️", id='download-global-params-btn-predict', className="icon-btn", title="Download CSV", style={"border": "none", "background": "none", "cursor": "pointer", "fontSize": "16px"})
                        ], style={"display": "flex", "gap": "4px"})
                    ], style={"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "8px"}),
                    html.Div(id='copy-global-params-feedback-predict', style={'fontSize': '12px', 'color': 'green', 'textAlign': 'center', 'marginBottom': '4px', 'minHeight': '16px'}),
                    html.Div(id='global-params-table-predict', className="table-container")
                ], className="panel", style={"marginTop": "16px"}),
                
                # Integration Events Table
                html.Div([
                    html.Div([
                        html.Div("Integration Events", className="panel-title"),
                        html.Div([
                            html.Button("📋", id='copy-integration-events-btn-predict', className="icon-btn", title="Copy to clipboard", style={"border": "none", "background": "none", "cursor": "pointer", "fontSize": "16px"}),
                            html.Button("⬇️", id='download-integration-events-btn-predict', className="icon-btn", title="Download CSV", style={"border": "none", "background": "none", "cursor": "pointer", "fontSize": "16px"})
                        ], style={"display": "flex", "gap": "4px"})
                    ], style={"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "8px"}),
                    html.Div(id='copy-integration-events-feedback-predict', style={'fontSize': '12px', 'color': 'green', 'textAlign': 'center', 'marginBottom': '4px', 'minHeight': '16px'}),
                    html.Div(id='integration-events-table-predict', className="table-container")
                ], className="panel", style={"marginTop": "16px"}),
            ], style={"display": "flex", "flexDirection": "column", "gap": "8px"}),
        ], style={"width": "408px", "marginLeft": "20px", "flex": "0 0 408px"}),
    ], className="main-content-flex", style={"display": "flex", "flexDirection": "row", "alignItems": "flex-start", "marginBottom": "0px"}),

    # Hidden Divs for data storage
    dcc.Store(id='stored-sample-data-predict'),
    dcc.Store(id='stored-background-data-predict'),
    dcc.Store(id='peak-data-store-predict'), # Changed from stored-peaks-data-predict to match callbacks
    dcc.Store(id='graph-zoom-state-predict'),
    dcc.Store(id='table-page-state-predict'),
    dcc.Store(id='api-result-store-predict'), # For API results if needed
    dcc.Store(id='upload-state-predict'), 
    dcc.Store(id='long-baselines-store-predict', data=[{"start": "", "end": ""}, {"start": "", "end": ""}]),
    dcc.Store(id='baseline-remove-nclicks-predict', data=[]),
    dcc.Store(id='baseline-lines-store-predict', data={}), # For storing baseline line coordinates
    dcc.Store(id='baseline-validation-store-predict', data={}), # For storing baseline validation states
    dcc.Store(id='global-params-store-predict'), # For global parameters data
    dcc.Store(id='integration-events-store-predict'), # For integration events data
    dcc.Interval(id='upload-status-checker-predict', interval=1*1000, n_intervals=0, disabled=True),
    dcc.Interval(id='copy-feedback-interval-predict', interval=2*1000, n_intervals=0, disabled=False),

    # Download components
    dcc.Download(id='download-peaks-csv-predict'), # For peak table download
    dcc.Download(id='download-demo-peaks-predict'), # For demo peak table
    dcc.Download(id='download-global-params-csv-predict'), # For global parameters download
    dcc.Download(id='download-integration-events-csv-predict'), # For integration events download

    # Hidden Divs for selections and statuses
    html.Div(id='selected-data-predict', style={'display': 'none'}),
    # html.Div(id='api-status-predict', className="status-message"), # Moved to peak table card

    # Cloud Upload Modal (match home_page.py design)
    html.Div(id='cloud-upload-modal-predict', className='modal', style={'display': 'none'}, children=[
        html.Div(className='modal-content', children=[
            html.H3(id='cloud-upload-title-predict', children="Cloud Upload Configuration", style={'textAlign': 'center'}),
            html.Div([
                html.Label("Project Name"),
                dcc.Input(
                    id='project-name-input-predict',
                    type='text',
                    value='',
                    placeholder='Enter project path...',
                    style={'width': '100%', 'marginBottom': '15px'}
                ),
                html.Label("Channel ID"),
                dcc.Input(
                    id='channel-id-input-predict',
                    type='text',
                    value='',
                    placeholder='Enter channel ID...',
                    style={'width': '100%', 'marginBottom': '15px'}
                ),
                html.Div(id='cloud-form-error-predict', className='error-message'),
                html.Div(id='cloud-upload-error-predict', className='error-message', style={'color': 'red', 'marginBottom': '15px'}),
                html.Div([
                    html.Button('Cancel', id='cloud-cancel-btn-predict', className='modal-btn'),
                    html.Button('Submit', id='cloud-submit-btn-predict', className='modal-btn primary')
                ], style={'display': 'flex', 'justifyContent': 'flex-end', 'gap': '10px'}),
                html.Div(id='cloud-upload-type-predict', style={'display': 'none'})
            ], style={'padding': '20px'})
        ])
    ]),

    # Cloud Loading Modal (match home_page.py design)
    html.Div(id='cloud-loading-modal-predict', className='modal', style={'display': 'none'}, children=[
        html.Div(className='modal-content', children=[
            html.H3("Loading Data", style={'textAlign': 'center'}),
            html.Div([
                html.Div(className='loading-spinner', style={'margin': '0 auto', 'width': '50px', 'height': '50px'}),
                html.Div(id='cloud-loading-status-predict', style={'textAlign': 'center', 'marginTop': '20px', 'color': '#666'})
            ], style={'padding': '20px'})
        ])
    ]),

    # Interval for selection checking (if needed for advanced interactions)
    dcc.Interval(id='selection-checker-predict', interval=500, n_intervals=0, disabled=True),

    # Modal for adding a new peak (Structure copied from home_page.py)
    html.Div([
        html.Div([
            html.H2('Add New Peak'),
            html.Label('Peak Name:'),
            dcc.Input(id='peak-name-input-predict', type='text', className="modal-input"),
            html.Label('Integration Type:'),
            dcc.Input(id='peak-inttype-input-predict', type='text', className="modal-input"),
            html.Label('Retention Time (min):'),
            dcc.Input(id='peak-rt-input-predict', type='number', className="modal-input"),
            html.Label('Start Time (min): *Required'),
            dcc.Input(id='peak-start-input-predict', type='number', required=True, className="modal-input"),
            html.Label('End Time (min): *Required'),
            dcc.Input(id='peak-end-input-predict', type='number', required=True, className="modal-input"),
            html.Label('Slope:'),
            dcc.Input(id='peak-slope-input-predict', type='text', className="modal-input"), # Changed to text to allow empty or numeric
            html.Label('Offset:'),
            dcc.Input(id='peak-offset-input-predict', type='text', className="modal-input"), # Changed to text to allow empty or numeric
            html.Div(id='peak-form-error-predict', className='error-message'),
            html.Div([
                html.Button('Cancel', id='cancel-peak-button-predict', className="modal-button cancel-button"),
                html.Button('Save Peak', id='save-peak-button-predict', className="modal-button submit-button")
            ], className="modal-buttons")
        ], className='modal-content')
    ], id='add-peak-modal-predict', className='modal', style={'display': 'none'}),

    # General Error Modal (copied from home_page.py)
    html.Div([
        html.Div([
            html.H2('Error'),
            html.P(id='error-message-predict'),
            html.Button('Close', id='close-error-button-predict', className="modal-button")
        ], className='modal-content error-modal-content')
    ], id='error-modal-predict', className='modal', style={'display': 'none'}),

    html.Div(id='upload-status-predict', className="status-message", style={'display':'none'}), # General status, initially hidden
    html.Div(id='feedback-save-status-predict', style={'display': 'none'}), # Feedback save status

]) 