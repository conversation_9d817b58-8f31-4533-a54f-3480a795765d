#!/usr/bin/env python3
"""
Database utilities for Chromatogram Viewer Application

This module provides:
1. Database connection management
2. Table creation and setup
3. Verification and testing tools
4. Task and feedback management functions

When run directly, it acts as an initialization and verification tool.
"""
import os
import sys
import psycopg2
import psycopg2.extras
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
import uuid
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('db_utils')

# Database connection details from environment variables with fallbacks
DB_HOST = os.environ.get('DB_HOST', '*************')
DB_PORT = os.environ.get('DB_PORT', '32020')
DB_NAME = os.environ.get('DB_NAME', 'chrom_assist')
DB_USER = os.environ.get('DB_USER', 'chrom_assist')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'jlPrOpCC0YbdNHEF6N5NKIFPIDCBA5mI5rw5yfdsrVoTHAqGygvmOeFBPuOeNO8S')

# SQL script for database setup
DB_SETUP_SQL = """
-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- PeakTable Tasks table
CREATE TABLE IF NOT EXISTS peaktable_tasks (
    task_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_submitter VARCHAR(255) NOT NULL,
    submit_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    payload JSONB NOT NULL,
    response_status VARCHAR(50) NOT NULL DEFAULT 'SUBMITTED',
    result_peaktable JSONB,
    peaktable_id UUID UNIQUE,
    completion_time TIMESTAMP WITH TIME ZONE,
    sample_source_type VARCHAR(50),
    sample_source_info VARCHAR(1024)
);

-- Create indices for better performance
CREATE INDEX IF NOT EXISTS idx_peaktable_tasks_peaktable_id ON peaktable_tasks(peaktable_id);
CREATE INDEX IF NOT EXISTS idx_peaktable_tasks_submit_time ON peaktable_tasks(submit_time DESC);
CREATE INDEX IF NOT EXISTS idx_peaktable_tasks_sample_source_type ON peaktable_tasks(sample_source_type);

-- PeakTable Feedbacks table
CREATE TABLE IF NOT EXISTS peaktable_feedbacks (
    id SERIAL PRIMARY KEY,
    peaktable_id UUID NOT NULL REFERENCES peaktable_tasks(peaktable_id),
    feedback_data JSONB,
    operation_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    operation_type VARCHAR(50),
    FOREIGN KEY (peaktable_id) REFERENCES peaktable_tasks(peaktable_id)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_peaktable_feedbacks_peaktable_id ON peaktable_feedbacks(peaktable_id);
"""

def get_db_connection() -> Optional[psycopg2.extensions.connection]:
    """
    Create and return a connection to the PostgreSQL database.
    
    Returns:
        connection: A PostgreSQL database connection or None if connection failed
    """
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        return conn
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        return None

def setup_database() -> bool:
    """
    Set up the database tables if they don't already exist.
    
    Returns:
        bool: True if successful, False if failed
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return False
            
        cursor = conn.cursor()
        
        # Create uuid-ossp extension if it doesn't exist
        cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")
        
        # Create peaktable_tasks table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS peaktable_tasks (
            task_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            request_submitter TEXT NOT NULL,
            submit_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            payload JSONB NOT NULL,
            response_status TEXT,
            response_result JSONB,
            peaktable_id UUID UNIQUE,
            completion_time TIMESTAMP WITH TIME ZONE,
            task_type TEXT DEFAULT 'peaktable',
            sample_source_type TEXT,
            sample_source_info TEXT
        );
        """)
        
        # Create peaktable_feedbacks table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS peaktable_feedbacks (
            feedback_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            peaktable_id UUID REFERENCES peaktable_tasks(peaktable_id),
            feedback_data JSONB NOT NULL,
            operation_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            operation_type TEXT NOT NULL
        );
        """)
        
        # Create index on peaktable_id if it doesn't exist
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_feedbacks_peaktable_id 
        ON peaktable_feedbacks(peaktable_id);
        """)
        
        # Create peakseg_tasks table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS peakseg_tasks (
            task_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            request_submitter TEXT NOT NULL,
            submit_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            payload JSONB NOT NULL,
            response_status TEXT,
            response_result JSONB,
            peakseg_id UUID UNIQUE,
            completion_time TIMESTAMP WITH TIME ZONE,
            task_type TEXT DEFAULT 'peakseg',
            sample_source_type TEXT,
            sample_source_info TEXT
        );
        """)
        
        # Create peakseg_feedbacks table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS peakseg_feedbacks (
            feedback_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            peakseg_id UUID REFERENCES peakseg_tasks(peakseg_id),
            feedback_data JSONB NOT NULL,
            operation_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            operation_type TEXT NOT NULL
        );
        """)
        
        # Create index on peakseg_id if it doesn't exist
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_peakseg_feedbacks_peakseg_id 
        ON peakseg_feedbacks(peakseg_id);
        """)
        
        conn.commit()
        
        # Now check if the sample_source columns exist, add them if not
        cursor.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'peaktable_tasks' AND column_name = 'sample_source_type';
        """)
        
        if cursor.rowcount == 0:
            # Add sample_source_type column
            cursor.execute("""
            ALTER TABLE peaktable_tasks 
            ADD COLUMN IF NOT EXISTS sample_source_type TEXT;
            """)
            
        cursor.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'peaktable_tasks' AND column_name = 'sample_source_info';
        """)
        
        if cursor.rowcount == 0:
            # Add sample_source_info column
            cursor.execute("""
            ALTER TABLE peaktable_tasks 
            ADD COLUMN IF NOT EXISTS sample_source_info TEXT;
            """)
            
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"Error setting up database: {e}")
        if 'conn' in locals() and conn:
            conn.rollback()
        return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def save_task(payload: Dict[str, Any], submitter: str, sample_source_type: str = None, sample_source_info: str = None) -> Optional[str]:
    """
    Save a task to the database.
    
    Args:
        payload: The task payload (request body)
        submitter: The identity of who/what submitted the task
        sample_source_type: Optional type of sample source (local_file or cloud)
        sample_source_info: Optional information about the sample source
        
    Returns:
        The task ID if successful, None if failed
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return None
            
        cursor = conn.cursor()
        
        # Generate a task_id
        task_id = str(uuid.uuid4())
        
        # Insert the task with current timestamp
        cursor.execute("""
            INSERT INTO peaktable_tasks 
            (task_id, payload, request_submitter, sample_source_type, sample_source_info, submit_time, response_status) 
            VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP, %s)
        """, (
            task_id, 
            json.dumps(payload) if isinstance(payload, dict) else payload, 
            submitter,
            sample_source_type,
            sample_source_info,
            "pending"
        ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Saved task with ID: {task_id}")
        return task_id
        
    except Exception as e:
        logger.error(f"Error saving task: {e}")
        return None
        
def update_task_with_response(task_id: str, status: str, result: Any = None) -> Optional[str]:
    """
    Update a task with the response status and result.
    
    Args:
        task_id: The task ID to update
        status: The response status
        result: The response body (optional)
        
    Returns:
        The peaktable_id if successful, None if failed
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return None
            
        cursor = conn.cursor()
        
        # Generate a peaktable_id if not already existing
        peaktable_id = str(uuid.uuid4())
        
        # Update the task with response info
        cursor.execute("""
            UPDATE peaktable_tasks
            SET response_status = %s,
                result_peaktable = %s,
                peaktable_id = %s,
                completion_time = CURRENT_TIMESTAMP
            WHERE task_id = %s
        """, (
            status,
            json.dumps(result) if isinstance(result, (dict, list)) else result,
            peaktable_id,
            task_id
        ))
        
        # Verify the update happened
        if cursor.rowcount > 0:
            logger.info(f"Updated task {task_id} with peaktable_id {peaktable_id}")
            conn.commit()
            conn.close()
            return peaktable_id
        else:
            logger.error(f"Task with ID {task_id} not found")
            conn.close()
            return None
            
    except Exception as e:
        logger.error(f"Error updating task: {e}")
        return None

def save_feedback(
    peaktable_id: str,
    feedback: Dict[str, Any],
    operation_type: str = "manual"
) -> bool:
    """
    Save feedback for a peak table result.
    
    Args:
        peaktable_id: The ID of the peak table
        feedback: The feedback data (JSON with peak information)
        operation_type: Type of operation being performed (manual, auto, etc.)
        
    Returns:
        bool: True if successful, False if failed
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return False
            
        cursor = conn.cursor()
        
        # Insert feedback with operation_time automatically set by PostgreSQL
        cursor.execute("""
            INSERT INTO peaktable_feedbacks (
                peaktable_id,
                feedback_data,
                operation_type
            ) VALUES (%s, %s, %s)
        """, (peaktable_id, psycopg2.extras.Json(feedback), operation_type))
        
        conn.commit()
        logger.info(f"Feedback saved successfully for peaktable: {peaktable_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error saving feedback: {e}")
        if 'conn' in locals() and conn:
            conn.rollback()
        return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_task_history(limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get recent task history with their results.
    
    Args:
        limit: Maximum number of tasks to return
        
    Returns:
        List[Dict[str, Any]]: List of task records
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return []
            
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute("""
            SELECT task_id, request_submitter, submit_time,
                   response_status, peaktable_id, completion_time,
                   payload, response_result, sample_source_type, sample_source_info
            FROM peaktable_tasks
            ORDER BY submit_time DESC
            LIMIT %s
        """, (limit,))
        
        tasks = cursor.fetchall()
        return [dict(task) for task in tasks]
        
    except Exception as e:
        logger.error(f"Error getting task history: {e}")
        return []
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_task_feedbacks(peaktable_id: str) -> List[Dict[str, Any]]:
    """
    Get all feedback for a specific peak table result.
    
    Args:
        peaktable_id: The ID of the peak table
        
    Returns:
        List[Dict[str, Any]]: List of feedback records
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return []
            
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute("""
            SELECT id, feedback_data, operation_time, operation_type
            FROM peaktable_feedbacks
            WHERE peaktable_id = %s
            ORDER BY operation_time DESC
        """, (peaktable_id,))
        
        feedbacks = cursor.fetchall()
        return [dict(feedback) for feedback in feedbacks]
        
    except Exception as e:
        logger.error(f"Error getting feedbacks: {e}")
        return []
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def test_connection() -> bool:
    """
    Test the database connection and verify tables exist.
    
    Returns:
        bool: True if connection successful and tables exist, False otherwise
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database for connection test")
            return False
        
        cursor = conn.cursor()
        
        # Check if tables exist
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'peaktable_tasks'
            )
        """)
        tasks_exists = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'peaktable_feedbacks'
            )
        """)
        feedbacks_exists = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        if tasks_exists and feedbacks_exists:
            logger.info("Database connection test successful, all tables exist")
            return True
        else:
            missing = []
            if not tasks_exists:
                missing.append('peaktable_tasks')
            if not feedbacks_exists:
                missing.append('peaktable_feedbacks')
            logger.warning(f"Database connection successful but tables missing: {', '.join(missing)}")
            return False
    except Exception as e:
        logger.error(f"Error testing database connection: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def update_peaktable_feedback(peaktable_id: str, feedback_data: Any, operation_type: str = "manual") -> bool:
    """
    Update an existing feedback record or create a new one for a peaktable.
    
    Args:
        peaktable_id: The ID of the peak table
        feedback_data: The feedback data (peak information)
        operation_type: Type of operation being performed
        
    Returns:
        bool: True if successful, False if failed
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return False
            
        cursor = conn.cursor()
        
        # Insert a new feedback record with current timestamp
        cursor.execute("""
            INSERT INTO peaktable_feedbacks
            (peaktable_id, feedback_data, operation_type)
            VALUES (%s, %s, %s)
        """, (
            peaktable_id,
            json.dumps(feedback_data) if isinstance(feedback_data, (dict, list)) else feedback_data,
            operation_type
        ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Updated feedback for peaktable_id: {peaktable_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating feedback: {e}")
        return False

def get_sample_source_info(peaktable_id: str) -> Dict[str, str]:
    """
    Get sample source information for a specific peaktable ID.
    
    Args:
        peaktable_id: The ID of the peak table
        
    Returns:
        Dict with sample_source_type and sample_source_info keys
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return {}
            
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute("""
            SELECT sample_source_type, sample_source_info 
            FROM peaktable_tasks 
            WHERE peaktable_id = %s
        """, (peaktable_id,))
        
        result = cursor.fetchone()
        if result:
            return {
                'sample_source_type': result['sample_source_type'],
                'sample_source_info': result['sample_source_info']
            }
        else:
            logger.warning(f"No sample source info found for peaktable_id: {peaktable_id}")
            return {}
            
    except Exception as e:
        logger.error(f"Error retrieving sample source info: {e}")
        return {}
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def save_peakseg_task(payload: Dict[str, Any], submitter: str, sample_source_type: str = None, sample_source_info: str = None) -> Optional[str]:
    """
    Save a peakseg task to the database.
    
    Args:
        payload: The task payload (request body)
        submitter: The identity of who/what submitted the task
        sample_source_type: Optional type of sample source (local_file or cloud)
        sample_source_info: Optional information about the sample source
        
    Returns:
        Optional[str]: The task_id if successful, None if failed
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return None
            
        cursor = conn.cursor()
        
        # Insert the task
        cursor.execute("""
            INSERT INTO peakseg_tasks 
            (request_submitter, payload, response_status, sample_source_type, sample_source_info)
            VALUES (%s, %s, %s, %s, %s)
            RETURNING task_id
        """, (
            submitter,
            json.dumps(payload) if isinstance(payload, (dict, list)) else payload,
            'SUBMITTED',
            sample_source_type,
            sample_source_info
        ))
        
        task_id = cursor.fetchone()[0]
        conn.commit()
        conn.close()
        
        logger.info(f"Saved peakseg task with ID: {task_id}")
        return str(task_id)
        
    except Exception as e:
        logger.error(f"Error saving peakseg task: {e}")
        return None

def update_peakseg_task_with_response(task_id: str, status: str, result: Any = None) -> Optional[str]:
    """
    Update a peakseg task with the API response.
    
    Args:
        task_id: The task ID to update
        status: The response status
        result: The result from the API
        
    Returns:
        Optional[str]: The peakseg_id if successful, None if failed
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return None
            
        cursor = conn.cursor()
        
        # Generate a unique peakseg_id
        peakseg_id = str(uuid.uuid4())
        
        # Update the task
        cursor.execute("""
            UPDATE peakseg_tasks 
            SET response_status = %s, response_result = %s, peakseg_id = %s, completion_time = %s
            WHERE task_id = %s
        """, (
            status,
            json.dumps(result) if isinstance(result, (dict, list)) else result,
            peakseg_id,
            datetime.now(),
            task_id
        ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Updated peakseg task {task_id} with status {status}")
        return peakseg_id
        
    except Exception as e:
        logger.error(f"Error updating peakseg task: {e}")
        return None

def update_peakseg_feedback(peakseg_id: str, feedback_data: Any, operation_type: str = "manual") -> bool:
    """
    Update feedback for a peakseg result.
    
    Args:
        peakseg_id: The ID of the peakseg result
        feedback_data: The feedback data (peak information)
        operation_type: Type of operation being performed
        
    Returns:
        bool: True if successful, False if failed
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return False
            
        cursor = conn.cursor()
        
        # Insert a new feedback record
        cursor.execute("""
            INSERT INTO peakseg_feedbacks
            (peakseg_id, feedback_data, operation_type)
            VALUES (%s, %s, %s)
        """, (
            peakseg_id,
            json.dumps(feedback_data) if isinstance(feedback_data, (dict, list)) else feedback_data,
            operation_type
        ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Updated feedback for peakseg_id: {peakseg_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating peakseg feedback: {e}")
        return False

def get_peakseg_task_history(limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get recent peakseg task history.
    
    Args:
        limit: Maximum number of tasks to return
        
    Returns:
        List[Dict[str, Any]]: List of task records
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return []
            
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute("""
            SELECT task_id, request_submitter, submit_time, response_status, 
                   peakseg_id, completion_time, sample_source_type, sample_source_info
            FROM peakseg_tasks
            ORDER BY submit_time DESC
            LIMIT %s
        """, (limit,))
        
        tasks = cursor.fetchall()
        return [dict(task) for task in tasks]
        
    except Exception as e:
        logger.error(f"Error getting peakseg task history: {e}")
        return []
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_peakseg_task_feedbacks(peakseg_id: str) -> List[Dict[str, Any]]:
    """
    Get all feedback for a specific peakseg result.
    
    Args:
        peakseg_id: The ID of the peakseg result
        
    Returns:
        List[Dict[str, Any]]: List of feedback records
    """
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database")
            return []
            
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute("""
            SELECT feedback_id, feedback_data, operation_time, operation_type
            FROM peakseg_feedbacks
            WHERE peakseg_id = %s
            ORDER BY operation_time DESC
        """, (peakseg_id,))
        
        feedbacks = cursor.fetchall()
        return [dict(feedback) for feedback in feedbacks]
        
    except Exception as e:
        logger.error(f"Error getting peakseg feedbacks: {e}")
        return []
    finally:
        if 'conn' in locals() and conn:
            conn.close()

# Initialize database on import
if __name__ != "__main__":
    try:
        setup_success = setup_database()
        if setup_success:
            logger.info("Database setup successful")
            test_connection()
        else:
            logger.warning("Database setup failed")
    except Exception as e:
        logger.error(f"Error during database initialization: {e}")

if __name__ == "__main__":
    success = setup_database() and test_connection()
    sys.exit(0 if success else 1) 