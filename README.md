1. Database connection:

[
  {
    "环境": "K8S_DEV",
    "外部访问地址": "*************",
    "主库端口": "32020",
    "从库端口": "32021",
    "k8s集群内部访问地址": "public-database.database",
    "库名": "chrom_assist",
    "账号": "chrom_assist"
    "密码": "jlPrOpCC0YbdNHEF6N5NKIFPIDCBA5mI5rw5yfdsrVoTHAqGygvmOeFBPuOeNO8S"
  }
]

2. Data table design

## PeakTable_Tasks: 

| Field Name | Data Type | Constraints/Attributes | Description |
|------------|-----------|------------------------|-------------|
| task_id | UUID | PK, NN | Unique identifier for the task. |
| request_submitter | VARCHAR(255) | NN | Email address or identifier of the user/system submitting the task. |
| submit_time | TIMESTAMP | NN | Timestamp when the task was submitted. |
| payload | JSON / JSONB | NN | Input parameters for the task, stored in JSON format. |
| response_status | VARCHAR(50) | NN | Current status of the task (e.g., '200', '505'). |
| result_peaktable | JSON / JSONB | NULL | The resulting peak table data in JSON format, populated upon success. |
| peaktable_id | UUID | NULL, UQ, INDEX | Unique identifier generated for the resulting peak table upon success. Links to peaktable_feedbacks. Null if task didn't succeed or produce a table. |
| completion_time | TIMESTAMP WITH TIME ZONE | NULL | Timestamp when the task finished processing (successfully or failed). |

## PeakTable_Feedbacks: 

| Field Name | Data Type | Constraints/Attributes | Description |
|------------|-----------|------------------------|-------------|
| peaktable_id | UUID | FK (-> peaktable_tasks.peaktable_id), NN, INDEX | Identifier of the peak table this feedback refers to. |
| feedback_comments | JSON / JSONB or TEXT | NN | The adjusted peak table in JSON format, with comment added to any selected rows. |

