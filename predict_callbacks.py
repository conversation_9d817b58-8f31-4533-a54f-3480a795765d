import dash
from dash.dependencies import Input, Output, State, ALL
from dash import Dash, dash_table, dcc, html, callback_context
import pandas as pd
import numpy as np
import base64
import io
import os
import tempfile
import json
import time
import datetime
from utils import (
    parse_excel, 
    parse_csv,
    save_peaktable_feedback,
    process_identify_peaks_api,
    save_peakseg_to_db,
    read_CH2,
    read_cdf,
    read_project_channel
)
from graph_utils import generate_sample_figure, create_figure

def register_predict_callbacks(app: Dash):
    """Registers all callbacks for the predict page."""
    # --- Cloud Upload Modal and State Management Callbacks ---
    @app.callback(
        [Output('cloud-upload-modal-predict', 'style'),
         Output('cloud-upload-type-predict', 'children'),
         Output('channel-id-input-predict', 'value'),
         Output('cloud-upload-title-predict', 'children'),
         Output('project-name-input-predict', 'placeholder'),
         Output('channel-id-input-predict', 'placeholder'),
         Output('project-name-input-predict', 'value')],
        [Input('sample-cloud-btn-predict', 'n_clicks'),
         Input('background-cloud-btn-predict', 'n_clicks'),
         Input('cloud-cancel-btn-predict', 'n_clicks')],
        [State('cloud-upload-modal-predict', 'style')]
    )
    def toggle_cloud_modal_predict(sample_clicks, background_clicks, cancel_clicks, current_style):
        ctx = dash.callback_context
        modal_style = {'display': 'none'}
        upload_type = ""
        channel_value = ""
        modal_title = dash.no_update
        project_placeholder = "Enter project path..."
        channel_placeholder = "Enter channel ID..."
        project_value = ""
        DEFAULT_PROJECT_NAME = '202407\\Clinical_Project\\*********'
        DEFAULT_SAMPLE_CHANNEL = '1522'
        DEFAULT_BACKGROUND_CHANNEL = '1502'
        if not ctx.triggered:
            return modal_style, upload_type, channel_value, modal_title, project_placeholder, channel_placeholder, project_value
        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
        if trigger_id == 'sample-cloud-btn-predict':
            modal_style = {'display': 'block'}
            upload_type = "sample"
            channel_value = ""
            modal_title = "Upload Sample from Empower"
            project_placeholder = DEFAULT_PROJECT_NAME
            channel_placeholder = DEFAULT_SAMPLE_CHANNEL
            project_value = ""
        elif trigger_id == 'background-cloud-btn-predict':
            modal_style = {'display': 'block'}
            upload_type = "background"
            channel_value = ""
            modal_title = "Upload Background from Empower"
            project_placeholder = DEFAULT_PROJECT_NAME
            channel_placeholder = DEFAULT_BACKGROUND_CHANNEL
            project_value = ""
        elif trigger_id == 'cloud-cancel-btn-predict':
            modal_style = {'display': 'none'}
            upload_type = ""
            channel_value = ""
            modal_title = "Cloud Upload Configuration"
            project_placeholder = "Enter project path..."
            channel_placeholder = "Enter channel ID..."
            project_value = ""
        elif current_style:
            modal_style = current_style
            project_placeholder = "Enter project path..."
            channel_placeholder = "Enter channel ID..."
            project_value = ""
            channel_value = ""
        return modal_style, upload_type, channel_value, modal_title, project_placeholder, channel_placeholder, project_value

    @app.callback(
        [Output('upload-state-predict', 'data'),
         Output('cloud-loading-modal-predict', 'style'),
         Output('cloud-upload-modal-predict', 'style', allow_duplicate=True),
         Output('cloud-loading-status-predict', 'children', allow_duplicate=True)],
        [Input('cloud-submit-btn-predict', 'n_clicks'),
         Input('stored-sample-data-predict', 'data'),
         Input('stored-background-data-predict', 'data'),
         Input('upload-status-checker-predict', 'n_intervals')],
        [State('upload-state-predict', 'data'),
         State('cloud-loading-modal-predict', 'style'),
         State('cloud-upload-modal-predict', 'style'),
         State('cloud-upload-type-predict', 'children')],
        prevent_initial_call=True
    )
    def manage_upload_state_predict(submit_clicks, sample_data, background_data, n_intervals,
                          current_state, loading_style, modal_style, upload_type):
        ctx = dash.callback_context
        if not ctx.triggered:
            if current_state and current_state.get('status') == 'uploading':
                 return {'status': 'idle', 'message': ''}, {'display': 'none'}, dash.no_update, ""
            return dash.no_update, dash.no_update, dash.no_update, dash.no_update
        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
        new_state = current_state if current_state else {
            'status': 'idle', 'message': '', 'start_time': None, 'data_received': False
        }
        output_loading_style = dash.no_update
        output_modal_style = dash.no_update
        output_loading_status = dash.no_update
        if trigger_id == 'cloud-submit-btn-predict' and submit_clicks:
            loading_message = "Loading data from Empower..."
            if upload_type == "sample":
                loading_message = "Loading sample data from Empower..."
            elif upload_type == "background":
                loading_message = "Loading background data from Empower..."
            new_state = {
                'status': 'uploading',
                'message': loading_message,
                'start_time': time.time(),
                'data_received': False
            }
            output_loading_style = {'display': 'block'}
            output_modal_style = {'display': 'none'}
            output_loading_status = loading_message
        elif trigger_id in ['stored-sample-data-predict', 'stored-background-data-predict']:
             if new_state.get('status') == 'uploading' and (sample_data or background_data):
                 new_state = {
                     'status': 'complete',
                     'message': 'Upload complete!',
                     'start_time': new_state.get('start_time'),
                     'finish_time': time.time(),
                     'data_received': True
                 }
                 output_loading_style = {'display': 'none'}
                 output_modal_style = {'display': 'none'}
                 output_loading_status = new_state['message']
        elif trigger_id == 'upload-status-checker-predict':
            if new_state.get('status') == 'uploading':
                elapsed_time = time.time() - new_state.get('start_time', time.time())
                TIMEOUT_SECONDS = 30
                if elapsed_time > TIMEOUT_SECONDS and not new_state.get('data_received', False):
                    new_state = {
                        'status': 'error',
                        'message': f'Upload timed out after {TIMEOUT_SECONDS} seconds. Please check connection or parameters.',
                        'start_time': new_state.get('start_time'),
                        'finish_time': time.time(),
                        'data_received': False
                    }
                    output_loading_style = {'display': 'none'}
                    output_modal_style = {'display': 'none'}
                    output_loading_status = new_state['message']
                else:
                     output_loading_status = new_state.get('message', 'Loading...')
                     output_loading_style = {'display': 'block'}
        final_state = new_state if new_state != current_state else dash.no_update
        return final_state, output_loading_style, output_modal_style, output_loading_status

    @app.callback(
        [Output('cloud-loading-status-predict', 'children', allow_duplicate=True),
         Output('cloud-loading-modal-predict', 'style', allow_duplicate=True)],
        Input('upload-state-predict', 'data'),
        prevent_initial_call=True
    )
    def update_loading_display_from_state_predict(current_state):
        if not current_state:
            return "", {'display': 'none'}
        status = current_state.get('status', 'idle')
        message = current_state.get('message', '')
        if status == 'uploading':
            return message, {'display': 'block'}
        elif status == 'complete' or status == 'error':
            return message, {'display': 'none'}
        else:
            return "", {'display': 'none'}

    @app.callback(
        Output('cloud-upload-error-predict', 'children'),
        [Input('cloud-submit-btn-predict', 'n_clicks')],
        [State('project-name-input-predict', 'value'),
         State('channel-id-input-predict', 'value'),
         State('cloud-upload-type-predict', 'children')],
        prevent_initial_call=True
    )
    def validate_cloud_inputs_predict(submit_clicks, project_name_in, channel_id_in, upload_type):
        DEFAULT_PROJECT_NAME = '202407\\Clinical_Project\\*********'
        DEFAULT_SAMPLE_CHANNEL = '1522'
        DEFAULT_BACKGROUND_CHANNEL = '1502'
        if not submit_clicks:
            return ""
        project_name = project_name_in if project_name_in else DEFAULT_PROJECT_NAME
        channel_id = channel_id_in
        if not channel_id:
            if upload_type == 'sample':
                channel_id = DEFAULT_SAMPLE_CHANNEL
            elif upload_type == 'background':
                channel_id = DEFAULT_BACKGROUND_CHANNEL
        if not project_name:
            return "Error: Project name is required"
        if not channel_id:
            return "Error: Channel ID is required"
        try:
            int(channel_id)
        except (ValueError, TypeError):
            return f"Error: Channel ID must be a number (Value: '{channel_id}')"
        return ""

    @app.callback(
        [Output('upload-status-predict', 'children', allow_duplicate=True),
         Output('stored-sample-data-predict', 'data', allow_duplicate=True),
         Output('stored-background-data-predict', 'data', allow_duplicate=True),
         Output('peak-data-store-predict', 'data', allow_duplicate=True),
         Output('peaks-api-status-predict', 'children', allow_duplicate=True),
         Output('upload-sample-predict', 'contents', allow_duplicate=True)],
        [Input('cloud-submit-btn-predict', 'n_clicks')],
        [State('project-name-input-predict', 'value'),
         State('channel-id-input-predict', 'value'),
         State('cloud-upload-type-predict', 'children')],
        prevent_initial_call=True
    )
    def handle_cloud_upload_predict(submit_clicks, project_name_in, channel_id_in, upload_type):
        DEFAULT_PROJECT_NAME = '202407\\Clinical_Project\\*********'
        DEFAULT_SAMPLE_CHANNEL = '1522'
        DEFAULT_BACKGROUND_CHANNEL = '1502'
        if not submit_clicks:
            return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        project_name = project_name_in if project_name_in else DEFAULT_PROJECT_NAME
        channel_id = channel_id_in
        if not channel_id:
            if upload_type == 'sample':
                channel_id = DEFAULT_SAMPLE_CHANNEL
            elif upload_type == 'background':
                channel_id = DEFAULT_BACKGROUND_CHANNEL
            else:
                 return "Error: Unknown upload type.", dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        if not project_name or not channel_id:
            return "Error: Project name and Channel ID are required.", dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        try:
            import types
            if not hasattr(dash.callback_context, "_chrome_app_project_name"):
                dash.callback_context._chrome_app_project_name = project_name
            else:
                setattr(dash.callback_context, "_chrome_app_project_name", project_name)
            channel_num = int(channel_id)
            df = read_project_channel(project_name, channel_num)
            if df is None:
                error_msg = f"Error: No data found for project '{project_name}' channel {channel_num}"
                return error_msg, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
            if len(df.columns) < 2:
                error_msg = "Error: Invalid data format - missing required time/signal columns"
                return error_msg, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
            data_type = "(Sample)" if upload_type == "sample" else "(Background)"
            legend_name = f"Channel {channel_num} {data_type}"
            formatted_data = {
                "time_steps": df.iloc[:, 0].tolist(),
                "signal": df.iloc[:, 1].tolist(),
                "columns": ["Time (min)", legend_name]
            }
            if upload_type == "sample":
                return "", formatted_data, dash.no_update, None, "", None
            else:
                return "", dash.no_update, formatted_data, dash.no_update, dash.no_update, dash.no_update
        except ValueError:
            error_msg = f"Error: Channel ID must be a number (Value received: '{channel_id}')"
            return error_msg, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        except Exception as e:
            error_msg = f"Error: Failed to fetch or process data - {str(e)}"
            return error_msg, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # --- File Upload Callbacks ---
    @app.callback(
        [Output('upload-status-predict', 'children'),
         Output('stored-sample-data-predict', 'data'),
         Output('peak-data-store-predict', 'data', allow_duplicate=True),
         Output('upload-sample-predict', 'contents')],
        Input('upload-sample-predict', 'contents'),
        State('upload-sample-predict', 'filename'),
        prevent_initial_call=True
    )
    def process_sample_upload_predict(contents, filename):
        if contents is None:
            return "", dash.no_update, dash.no_update, dash.no_update
        try:
            df = None
            if filename.endswith(('.xlsx', '.xls')):
                df = parse_excel(contents)
            elif filename.endswith('.csv'):
                df = parse_csv(contents)
            elif filename.endswith('.CH2'):
                content_type, content_string = contents.split(',')
                decoded = base64.b64decode(content_string)
                with tempfile.NamedTemporaryFile(suffix='.CH2', delete=False) as temp_file:
                    temp_file.write(decoded)
                    temp_path = temp_file.name
                df = read_CH2(temp_path)
                os.unlink(temp_path)
            elif filename.endswith('.cdf'):
                content_type, content_string = contents.split(',')
                decoded = base64.b64decode(content_string)
                with tempfile.NamedTemporaryFile(suffix='.cdf', delete=False) as temp_file:
                    temp_file.write(decoded)
                    temp_path = temp_file.name
                df = read_cdf(temp_path)
                os.unlink(temp_path)
            else:
                return f"Error: Invalid file format. Supported formats are .xlsx, .xls, .csv, .CH2, and .cdf", dash.no_update, dash.no_update, dash.no_update
            if df is None or df.empty:
                return "Error: Empty file or file could not be parsed", dash.no_update, dash.no_update, dash.no_update
            if len(df.columns) < 2:
                return "Error: File must contain time and signal columns", dash.no_update, dash.no_update, dash.no_update
            formatted_data = {
                "time_steps": df.iloc[:, 0].tolist(),
                "signal": df.iloc[:, 1].tolist(),
                "columns": ["Time (min)", filename]
            }
            return "", formatted_data, None, None
        except Exception as e:
            return f"Error processing file: {str(e)}", dash.no_update, dash.no_update, dash.no_update

    @app.callback(
        [Output('upload-status-predict', 'children', allow_duplicate=True),
         Output('stored-background-data-predict', 'data', allow_duplicate=True)],
        [Input('upload-background-predict', 'contents')],
        [State('upload-background-predict', 'filename')],
        prevent_initial_call=True
    )
    def handle_uploads_predict(background_contents, background_filename):
        ctx = dash.callback_context
        trigger = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else None
        status_message = ""
        background_data = dash.no_update
        if trigger == 'upload-background-predict' and background_contents:
            try:
                background_df = None
                if background_filename.endswith(('.xlsx', '.xls')):
                    background_df = parse_excel(background_contents)
                elif background_filename.endswith('.csv'):
                    background_df = parse_csv(background_contents)
                elif background_filename.endswith('.CH2'):
                    content_type, content_string = background_contents.split(',')
                    decoded = base64.b64decode(content_string)
                    with tempfile.NamedTemporaryFile(suffix='.CH2', delete=False) as temp_file:
                        temp_file.write(decoded)
                        temp_path = temp_file.name
                    background_df = read_CH2(temp_path)
                    os.unlink(temp_path)
                elif background_filename.endswith('.cdf'):
                    content_type, content_string = background_contents.split(',')
                    decoded = base64.b64decode(content_string)
                    with tempfile.NamedTemporaryFile(suffix='.cdf', delete=False) as temp_file:
                        temp_file.write(decoded)
                        temp_path = temp_file.name
                    background_df = read_cdf(temp_path)
                    os.unlink(temp_path)
                else:
                    return "Error: Invalid file format for background", dash.no_update
                if background_df is not None:
                    bg_name = os.path.splitext(background_filename)[0]
                    background_data = {
                        "time_steps": background_df.iloc[:, 0].tolist(),
                        "signal": background_df.iloc[:, 1].tolist(),
                        "columns": ["Time (min)", bg_name]
                    }
                    return "", background_data
                else:
                    return f"Error: Could not parse background file {background_filename}", dash.no_update
            except Exception as e:
                return str(e), dash.no_update
        return "", dash.no_update

    # --- Chromatogram Plot Update ---
    @app.callback(
        Output('chromatogram-plot-predict', 'figure'),
        [Input('stored-sample-data-predict', 'data'),
         Input('stored-background-data-predict', 'data'),
         Input('peak-data-store-predict', 'data'),
         Input('baseline-lines-store-predict', 'data'),
         Input('reset-button-predict', 'n_clicks')],
        [State('graph-zoom-state-predict', 'data'),
         State('chromatogram-plot-predict', 'figure'),
         State('upload-sample-predict', 'filename'),
         State('upload-background-predict', 'filename')],
    )
    def update_graph_predict(sample_data, background_data, peaks_data, baseline_lines, n_clicks,
                             zoom_state, current_figure, sample_filename, background_filename):
        ctx = dash.callback_context
        trigger = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else None

        # Convert stored data to dataframes if available
        sample_df = None
        if sample_data and 'time_steps' in sample_data and 'signal' in sample_data:
            sample_df = pd.DataFrame({
                "time_steps": sample_data["time_steps"],
                "signal": sample_data["signal"]
            })
            if "columns" in sample_data and len(sample_data["columns"]) >= 2:
                sample_df.columns = sample_data["columns"]
            else:
                sample_df.columns = ["Time (min)", "Sample"]

        background_df = None
        if background_data and 'time_steps' in background_data and 'signal' in background_data:
            background_df = pd.DataFrame({
                "time_steps": background_data["time_steps"],
                "signal": background_data["signal"]
            })
            if "columns" in background_data and len(background_data["columns"]) >= 2:
                background_df.columns = background_data["columns"]
            else:
                background_df.columns = ["Time (min)", "Background"]
        
        # Process peaks data
        peaks_df = pd.DataFrame(peaks_data) if peaks_data and len(peaks_data) > 0 else None
        
        # Handle reset button - only reset if specifically clicked
        reset = trigger == 'reset-button-predict'
        
        # Check if we're updating due to peaks or baselines changing
        peaks_changed = trigger == 'peak-data-store-predict'
        baselines_changed = trigger == 'baseline-lines-store-predict'
        current_x_range = None
        current_y_range = None
        if (peaks_changed or baselines_changed) and current_figure and 'layout' in current_figure:
            if 'xaxis' in current_figure['layout'] and 'range' in current_figure['layout']['xaxis']:
                current_x_range = current_figure['layout']['xaxis']['range']
            if 'yaxis' in current_figure['layout'] and 'range' in current_figure['layout']['yaxis']:
                current_y_range = current_figure['layout']['yaxis']['range']
        
        # Create figure if data is available
        if sample_df is not None or background_df is not None:
            # Only pass filenames if they exist and the data doesn't come from SMB
            use_sample_filename = (sample_filename and sample_df is not None and
                                 not sample_df.columns[1].startswith('Channel'))
            use_bg_filename = (background_filename and background_df is not None and
                             not background_df.columns[1].startswith('Channel'))
            
            fig = create_figure(
                sample_df,
                background_df,
                peaks_df,
                baseline_lines=baseline_lines,
                reset=reset,
                sample_filename=sample_filename if use_sample_filename else None,
                background_filename=background_filename if use_bg_filename else None
            )
            
            # Apply zoom state when appropriate
            # If peaks changed and we have current ranges, apply them
            if peaks_changed and current_x_range and current_y_range and peaks_data is not None:
                fig['layout']['xaxis']['range'] = current_x_range
                fig['layout']['xaxis']['autorange'] = False
                fig['layout']['yaxis']['range'] = current_y_range
                fig['layout']['yaxis']['autorange'] = False
            # If baselines changed and we have current ranges, apply them
            elif baselines_changed and current_x_range and current_y_range:
                fig['layout']['xaxis']['range'] = current_x_range
                fig['layout']['xaxis']['autorange'] = False
                fig['layout']['yaxis']['range'] = current_y_range
                fig['layout']['yaxis']['autorange'] = False
            # Otherwise, use stored zoom state unless reset was clicked or new data was loaded
            elif zoom_state and not reset and trigger not in ['stored-sample-data-predict', 'stored-background-data-predict'] and not (trigger == 'baseline-lines-store-predict' and not current_x_range):
                if 'xaxis.range[0]' in zoom_state and 'xaxis.range[1]' in zoom_state:
                    fig['layout']['xaxis']['range'] = [zoom_state['xaxis.range[0]'], zoom_state['xaxis.range[1]']]
                    fig['layout']['xaxis']['autorange'] = False
                if 'yaxis.range[0]' in zoom_state and 'yaxis.range[1]' in zoom_state:
                    fig['layout']['yaxis']['range'] = [zoom_state['yaxis.range[0]'], zoom_state['yaxis.range[1]']]
                    fig['layout']['yaxis']['autorange'] = False
            return fig

        # Return sample figure when no data is loaded
        return generate_sample_figure()

    # --- Save graph's zoom state ---
    @app.callback(
        Output('graph-zoom-state-predict', 'data'),
        [Input('chromatogram-plot-predict', 'relayoutData')],
        [State('graph-zoom-state-predict', 'data')],
        prevent_initial_call=True
    )
    def save_zoom_state_predict(relayoutData, current_state):
        if relayoutData:
            # Complete zoom range is being set
            if 'xaxis.range[0]' in relayoutData and 'xaxis.range[1]' in relayoutData:
                return relayoutData
            # Autorange (reset) is being applied - keep current state
            elif 'xaxis.autorange' in relayoutData and relayoutData['xaxis.autorange']:
                if 'xaxis.autorange' in relayoutData or 'yaxis.autorange' in relayoutData:
                    # If explicitly setting autorange to true, clear the zoom state
                    if (relayoutData.get('xaxis.autorange') is True or 
                        relayoutData.get('yaxis.autorange') is True):
                        return {}
                return current_state
            # Partial update to existing state
            elif current_state and any(k.startswith('xaxis') or k.startswith('yaxis') for k in relayoutData):
                updated_state = dict(current_state)
                updated_state.update(relayoutData)
                return updated_state
        return current_state if current_state else dash.no_update

    # --- Peak Table Upload/Add/Edit/Display ---
    @app.callback(
        [Output('peaks-upload-status-predict', 'children'),
         Output('peaks-table-container-predict', 'children'),
         Output('peak-data-store-predict', 'data', allow_duplicate=True),
         Output('error-modal-predict', 'style', allow_duplicate=True),
         Output('error-message-predict', 'children', allow_duplicate=True)],
        [Input('upload-peaks-predict', 'contents'),
         Input('save-peak-button-predict', 'n_clicks'),
         Input('peak-data-store-predict', 'data')],
        [State('upload-peaks-predict', 'filename'),
         State('peak-name-input-predict', 'value'),
         State('peak-inttype-input-predict', 'value'),
         State('peak-rt-input-predict', 'value'),
         State('peak-start-input-predict', 'value'),
         State('peak-end-input-predict', 'value'),
         State('peak-slope-input-predict', 'value'),
         State('peak-offset-input-predict', 'value'),
         State('table-page-state-predict', 'data')],
        prevent_initial_call=True
    )
    def update_peaks_table_predict(contents, save_clicks, api_peaks_data, filename,
                           peak_name, peak_inttype, peak_rt, peak_start, peak_end, peak_slope, peak_offset,
                           page_state):
        ctx = dash.callback_context
        trigger = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else 'No trigger'
        status_message = ""
        page_current = page_state.get('current_page', 0) if page_state else 0
        
        try:
            # Handle API data update (when peaks come from Identify Peaks API)
            if trigger == 'peak-data-store-predict' and api_peaks_data:
                # API data is already in the correct format, just display it
                df = pd.DataFrame(api_peaks_data)
                status_message = f"Found {len(api_peaks_data)} peaks."
                
            elif trigger == 'upload-peaks-predict' and contents:
                df = None
                if filename.endswith(('.xlsx', '.xls')):
                    df = parse_excel(contents)
                elif filename.endswith('.csv'):
                    df = parse_csv(contents)
                else:
                    return "Invalid file format", dash.no_update, dash.no_update, {'display': 'block'}, "Please upload .csv or .xlsx files."

                required = {"StartTime", "EndTime", "IntType"}
                if not required.issubset(df.columns):
                    raise ValueError(f"Missing required columns: {', '.join(required - set(df.columns))}")

                status_message = f"Loaded {len(df)} peaks from {filename}"

            elif trigger == 'save-peak-button-predict' and save_clicks:
                if peak_start is not None and peak_end is not None:
                    new_peak = {
                        "Name": peak_name or "", "IntType": peak_inttype or "", "RetentionTime": peak_rt,
                        "StartTime": peak_start, "EndTime": peak_end, "Slope": peak_slope, "Offset": peak_offset, "Comment": ""
                    }
                    if api_peaks_data:
                        df = pd.DataFrame(api_peaks_data)
                        df = pd.concat([df, pd.DataFrame([new_peak])], ignore_index=True)
                    else:
                        df = pd.DataFrame([new_peak])
                    status_message = "Peak added."
                else:
                    status_message = "Failed to add peak: Start and End time required."
                    if api_peaks_data:
                        df = pd.DataFrame(api_peaks_data)
                    else:
                        df = pd.DataFrame()
            else:
                # Default case - either no trigger or unhandled trigger
                if api_peaks_data:
                    df = pd.DataFrame(api_peaks_data)
                else:
                    df = pd.DataFrame()
            
            if df.empty:
                table = dash_table.DataTable(id='peaks-data-table-predict', columns=[{"name": "Peak #", "id": "Row"}], data=[])
                return "No peaks data.", table, [], dash.no_update, dash.no_update

            all_cols = ["Name", "IntType", "RetentionTime", "StartTime", "EndTime", "Slope", "Offset", "Comment"]
            for col in all_cols:
                if col not in df.columns:
                    df[col] = "" if col in ["Name", "IntType", "Comment"] else pd.NA

            numeric_cols = ["RetentionTime", "StartTime", "EndTime", "Slope", "Offset"]
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            df.dropna(subset=['StartTime', 'EndTime'], inplace=True)
            
            df = df.sort_values(by='StartTime', ascending=True).reset_index(drop=True)
            final_peaks_data = df.to_dict('records')

            columns = [
                {"name": "Peak #", "id": "Row", "deletable": False, "editable": False},
                {"name": "Name", "id": "Name", "type": "text", "editable": True},
                {"name": "Integration Type", "id": "IntType", "type": "text", "editable": True},
                {"name": "Retention Time (min)", "id": "RetentionTime", "type": "numeric", "format": {"specifier": ".4f"}},
                {"name": "Start Time", "id": "StartTime", "type": "numeric", "format": {"specifier": ".4f"}},
                {"name": "End Time", "id": "EndTime", "type": "numeric", "format": {"specifier": ".4f"}},
                {"name": "Slope", "id": "Slope", "type": "numeric", "format": {"specifier": ".8f"}},
                {"name": "Offset", "id": "Offset", "type": "numeric", "format": {"specifier": ".8f"}},
                {"name": "Comment", "id": "Comment", "type": "text", "editable": True},
            ]
            table_data = [{**peak, "Row": i+1} for i, peak in enumerate(final_peaks_data)]

            table = dash_table.DataTable(
                id='peaks-data-table-predict',
                columns=columns,
                data=table_data,
                editable=True,
                row_deletable=True,
                style_table={'width': '100%'},
                style_cell={'textAlign': 'left', 'padding': '8px', 'minWidth': '80px'},
                style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'},
                style_data_conditional=[{'if': {'row_index': 'odd'}, 'backgroundColor': 'rgb(248, 248, 248)'}],
                page_size=10,
                page_action='native',
                page_current=page_current
            )
            return status_message, table, final_peaks_data, dash.no_update, dash.no_update

        except Exception as e:
            import traceback
            traceback.print_exc()
            return "An error occurred", html.Div(f"Error: {e}"), api_peaks_data if api_peaks_data else [], {'display': 'block'}, str(e)

    # --- Add Peak Modal Show/Hide ---
    @app.callback(
        Output('add-peak-modal-predict', 'style'),
        [Input('add-row-button-predict', 'n_clicks'),
         Input('cancel-peak-button-predict', 'n_clicks'),
         Input('save-peak-button-predict', 'n_clicks')],
        [State('add-peak-modal-predict', 'style')],
        prevent_initial_call=True
    )
    def toggle_peak_modal_predict(add_clicks, cancel_clicks, save_clicks, style):
        ctx = dash.callback_context
        if not ctx.triggered:
            return style
        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
        if trigger_id == 'add-row-button-predict':
            return {'display': 'block'}
        elif trigger_id in ['cancel-peak-button-predict', 'save-peak-button-predict']:
            return {'display': 'none'}
        return style

    # --- Download Peak Table CSV ---
    @app.callback(
        Output("download-peaks-csv-predict", "data"),
        [Input("download-button-predict", "n_clicks")],
        [State("peak-data-store-predict", "data")],
        prevent_initial_call=True
    )
    def download_peaks_csv_predict(n_clicks, stored_data):
        if not stored_data:
            return dash.no_update
        df = pd.DataFrame(stored_data)
        filename = f"Peak_table_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return dcc.send_data_frame(df.to_csv, filename, index=False)

    # --- Download Demo Peak Table ---
    @app.callback(
        Output("download-demo-peaks-predict", "data"),
        Input("download-demo-peaks-btn-predict", "n_clicks"),
        prevent_initial_call=True
    )
    def download_demo_peaks_predict(n_clicks):
        return dcc.send_file("templates/peak_table_demo.xlsx")

    # --- Error Modal Close ---
    @app.callback(
        Output('error-modal-predict', 'style', allow_duplicate=True),
        [Input('close-error-button-predict', 'n_clicks')],
        prevent_initial_call=True
    )
    def close_error_modal(n_clicks):
        if n_clicks:
            return {'display': 'none'}
        return dash.no_update

    # --- Table Page State ---
    @app.callback(
        Output('table-page-state-predict', 'data'),
        [Input('peaks-data-table-predict', 'page_current')],
        prevent_initial_call=True
    )
    def store_current_page_predict(page_current):
        return {'current_page': page_current}

    # --- Validate Add Peak Modal Form ---
    @app.callback(
        [Output('peak-form-error-predict', 'children'),
         Output('save-peak-button-predict', 'disabled')],
        [Input('peak-start-input-predict', 'value'),
         Input('peak-end-input-predict', 'value')]
    )
    def validate_peak_form_predict(start_time, end_time):
        if start_time is None or end_time is None:
            return "Start Time and End Time are required", True
        return "", False

    # --- Long Baselines Dynamic Panel ---
    # Store for baselines
    @app.callback(
        [Output('long-baselines-store-predict', 'data'),
         Output('baseline-remove-nclicks-predict', 'data')],
        Input('add-baseline-predict', 'n_clicks'),
        Input({'type': 'remove-baseline-predict', 'index': ALL}, 'n_clicks'),
        State('long-baselines-store-predict', 'data'),
        State('baseline-remove-nclicks-predict', 'data'),
        prevent_initial_call=True
    )
    def update_baselines(add_clicks, remove_clicks, baselines, prev_nclicks):
        ctx = callback_context
        if baselines is None:
            baselines = [{"start": "", "end": ""}, {"start": "", "end": ""}]
        if prev_nclicks is None or len(prev_nclicks) != len(remove_clicks):
            prev_nclicks = [0] * len(remove_clicks)
        if not ctx.triggered:
            return baselines, remove_clicks
        trigger = ctx.triggered[0]['prop_id']
        # Add baseline
        if 'add-baseline-predict' in trigger:
            baselines.append({"start": "", "end": ""})
            return baselines, remove_clicks
        # Remove baseline only if n_clicks increased
        elif 'remove-baseline-predict' in trigger:
            for idx, (curr, prev) in enumerate(zip(remove_clicks, prev_nclicks)):
                if curr is not None and curr > prev:
                    if 0 <= idx < len(baselines):
                        baselines.pop(idx)
                        remove_clicks.pop(idx)
                        prev_nclicks.pop(idx)
                        break
            return baselines, remove_clicks
        return baselines, remove_clicks

    # Render the baseline rows
    @app.callback(
        Output('long-baselines-container-predict', 'children'),
        Input('long-baselines-store-predict', 'data'),
        prevent_initial_call=False
    )
    def render_baseline_rows(baselines):
        if baselines is None:
            baselines = [{"start": "", "end": ""}, {"start": "", "end": ""}]
        rows = []
        for i, baseline in enumerate(baselines):
            rows.append(html.Div([
                dcc.Input(id={"type": "baseline-start-predict", "index": i}, type="number", placeholder="Start", value=baseline.get("start", ""), style={"width": "80px", "marginRight": "8px"}),
                dcc.Input(id={"type": "baseline-end-predict", "index": i}, type="number", placeholder="End", value=baseline.get("end", ""), style={"width": "80px", "marginRight": "8px"}),
                html.Button("check", id={"type": "check-baseline-predict", "index": i}, n_clicks=0, title="Check baseline coordinates", style={"background": "none", "border": "1px solid #4CAF50", "borderRadius": "3px", "color": "#4CAF50", "fontSize": "12px", "cursor": "pointer", "marginRight": "8px", "padding": "2px 6px", "height": "24px"}),
                html.Span(id={"type": "baseline-validation-predict", "index": i}, style={"fontSize": "12px", "fontWeight": "bold", "marginRight": "8px", "minWidth": "50px"}),
                html.Button("✖", id={"type": "remove-baseline-predict", "index": i}, n_clicks=0, style={"background": "none", "border": "none", "color": "#888", "fontSize": "22px", "cursor": "pointer", "marginLeft": "4px"}),
            ], style={"display": "flex", "alignItems": "center", "marginBottom": "8px"}))
        return rows

    # Update store when user edits a baseline input
    @app.callback(
        Output('long-baselines-store-predict', 'data', allow_duplicate=True),
        Input({'type': 'baseline-start-predict', 'index': ALL}, 'value'),
        Input({'type': 'baseline-end-predict', 'index': ALL}, 'value'),
        State('long-baselines-store-predict', 'data'),
        prevent_initial_call=True
    )
    def update_baseline_values(starts, ends, baselines):
        if baselines is None:
            baselines = []
        for i in range(min(len(baselines), len(starts), len(ends))):
            baselines[i]['start'] = starts[i]
            baselines[i]['end'] = ends[i]
        return baselines

    # Handle baseline check button clicks
    @app.callback(
        [Output('peaks-api-status-predict', 'children', allow_duplicate=True),
         Output('baseline-lines-store-predict', 'data', allow_duplicate=True),
         Output('baseline-validation-store-predict', 'data', allow_duplicate=True),
         Output({'type': 'baseline-validation-predict', 'index': ALL}, 'children'),
         Output({'type': 'baseline-validation-predict', 'index': ALL}, 'style')],
        Input({'type': 'check-baseline-predict', 'index': ALL}, 'n_clicks'),
        [State('long-baselines-store-predict', 'data'),
         State('stored-sample-data-predict', 'data'),
         State('baseline-lines-store-predict', 'data'),
         State('baseline-validation-store-predict', 'data')],
        prevent_initial_call=True
    )
    def check_baseline_coordinates(check_clicks, baselines_data, sample_data, current_lines, validation_store):
        from utils import calculate_baseline_coordinates
        
        # Determine the number of baselines first to know what to return for ALL outputs
        num_baselines = len(baselines_data) if baselines_data else 0
        
        # For 0 baselines, ALL outputs must be empty lists
        if num_baselines == 0:
            empty_validation_outputs = ([], [])
        else:
            empty_validation_outputs = (dash.no_update, dash.no_update)
        
        ctx = callback_context
        if not ctx.triggered:
            return dash.no_update, dash.no_update, dash.no_update, empty_validation_outputs[0], empty_validation_outputs[1]
            
        # If no check buttons exist (empty list), return empty lists for ALL outputs
        if not check_clicks:
            return dash.no_update, dash.no_update, {}, [], []
            
        # If no check buttons were actually clicked (all are None), don't process
        if all(clicks is None for clicks in check_clicks):
            return dash.no_update, dash.no_update, dash.no_update, empty_validation_outputs[0], empty_validation_outputs[1]
            
        # Initialize validation store if None
        if validation_store is None:
            validation_store = {}
        
        # Initialize validation arrays to match the current number of baselines
        validation_texts = [""] * num_baselines
        validation_styles = [{"fontSize": "12px", "fontWeight": "bold", "marginRight": "8px", "minWidth": "50px"}] * num_baselines
        
        # Restore validation results from store
        for i in range(num_baselines):
            if str(i) in validation_store:
                stored_validation = validation_store[str(i)]
                validation_texts[i] = stored_validation.get('text', '')
                if stored_validation.get('style'):
                    validation_styles[i] = stored_validation['style']
        
        # Check if sample data is empty
        if not sample_data:
            return "Error: No sample data loaded. Please upload sample data first.", dash.no_update, validation_store, validation_texts, validation_styles
            
        # Check if no baselines exist
        if num_baselines == 0:
            return "Error: No baselines exist.", dash.no_update, validation_store, [], []
            
        # Find which check button was clicked
        trigger = ctx.triggered[0]['prop_id']
        if 'check-baseline-predict' not in trigger:
            return dash.no_update, dash.no_update, dash.no_update, empty_validation_outputs[0], empty_validation_outputs[1]
            
        # Extract the index from the trigger
        import json
        trigger_id = json.loads(trigger.split('.')[0])
        clicked_index = trigger_id['index']
        
        # Check if we have baseline data and the clicked index exists
        if not baselines_data or clicked_index >= len(baselines_data):
            return "Error: Invalid baseline index.", dash.no_update, validation_store, validation_texts, validation_styles
            
        baseline = baselines_data[clicked_index]
        start_time = baseline.get('start', '')
        end_time = baseline.get('end', '')
        
        # Validate that both start and end times are provided
        if start_time == '' or end_time == '' or start_time is None or end_time is None:
            return f"Error: Please enter both start and end times for baseline {clicked_index + 1}.", dash.no_update, validation_store, validation_texts, validation_styles
            
        try:
            start_time = float(start_time)
            end_time = float(end_time)
        except (ValueError, TypeError):
            return f"Error: Start and end times must be valid numbers for baseline {clicked_index + 1}.", dash.no_update, validation_store, validation_texts, validation_styles
            
        if start_time >= end_time:
            return f"Error: End time must be greater than start time for baseline {clicked_index + 1}.", dash.no_update, validation_store, validation_texts, validation_styles
            
        # Calculate baseline coordinates using the utility function
        result = calculate_baseline_coordinates(sample_data, start_time, end_time)
        
        if result is None:
            return f"Error: Failed to calculate baseline coordinates for baseline {clicked_index + 1}.", dash.no_update, validation_store, validation_texts, validation_styles
            
        # Update baseline lines store
        if current_lines is None:
            current_lines = {}
        
        start_coords = result['start_coords']
        end_coords = result['end_coords']
        slope = result['slope']
        offset = result['offset']
        
        # Store the line data for this baseline index along with original baseline values
        current_lines[str(clicked_index)] = {
            'start_coords': start_coords,
            'end_coords': end_coords,
            'slope': slope,
            'offset': offset,
            'baseline_start': start_time,  # Store original baseline values
            'baseline_end': end_time       # for proper matching
        }
        
        # Check validation result and create appropriate message
        is_valid = result.get('is_valid', True)
        validation_status = "Valid" if is_valid else "Invalid"
        status_icon = "✓" if is_valid else "⚠"
        
        # Update validation display for the clicked baseline (with bounds check)
        if clicked_index < len(validation_texts):
            validation_texts[clicked_index] = validation_status
            validation_color = "#4CAF50" if is_valid else "#f44336"  # Green for valid, red for invalid
            validation_styles[clicked_index] = {
                "fontSize": "12px", 
                "fontWeight": "bold", 
                "marginRight": "8px", 
                "minWidth": "50px",
                "color": validation_color
            }
            
            # Store validation result in the store
            validation_store[str(clicked_index)] = {
                'text': validation_status,
                'style': validation_styles[clicked_index]
            }
        
        # Return success message with validation status and updated lines
        success_message = f"{status_icon} Baseline {clicked_index + 1} calculated: Start({start_coords[0]:.3f}, {start_coords[1]:.2f}), End({end_coords[0]:.3f}, {end_coords[1]:.2f}), Slope={slope:.6f}, Offset={offset:.6f} - {validation_status}"
        
        return success_message, current_lines, validation_store, validation_texts, validation_styles

    # Clean up baseline lines when baselines are removed
    @app.callback(
        Output('baseline-lines-store-predict', 'data', allow_duplicate=True),
        Input('long-baselines-store-predict', 'data'),
        State('baseline-lines-store-predict', 'data'),
        prevent_initial_call=True
    )
    def sync_baseline_lines_with_baselines(baselines_data, current_lines):
        if current_lines is None:
            current_lines = {}
        
        if not baselines_data:
            return {}
        
        # Strategy: Match lines to baselines by their actual start/end values
        # This ensures perfect synchronization regardless of index changes
        
        updated_lines = {}
        
        for i, baseline in enumerate(baselines_data):
            baseline_start = baseline.get("start", "")
            baseline_end = baseline.get("end", "")
            
            # Skip empty baselines
            if baseline_start == "" or baseline_end == "":
                continue
                
            try:
                baseline_start_float = float(baseline_start)
                baseline_end_float = float(baseline_end)
            except (ValueError, TypeError):
                continue
            
            # Find matching line by comparing baseline values
            matching_line = None
            for line_key, line_data in current_lines.items():
                if 'baseline_start' in line_data and 'baseline_end' in line_data:
                    line_start = line_data['baseline_start']
                    line_end = line_data['baseline_end']
                    
                    # Check if this line matches the current baseline (with small tolerance for float comparison)
                    if (abs(line_start - baseline_start_float) < 0.001 and 
                        abs(line_end - baseline_end_float) < 0.001):
                        matching_line = line_data
                        break
            
            # If we found a matching line, store it with the new index
            if matching_line:
                updated_lines[str(i)] = matching_line
        
        return updated_lines

    # Clean up validation store when baselines are removed
    @app.callback(
        Output('baseline-validation-store-predict', 'data', allow_duplicate=True),
        Input('long-baselines-store-predict', 'data'),
        State('baseline-validation-store-predict', 'data'),
        prevent_initial_call=True
    )
    def cleanup_validation_store(baselines_data, validation_store):
        if not validation_store:
            return {}
            
        num_baselines = len(baselines_data) if baselines_data else 0
        
        # Keep only validation results for existing baselines
        cleaned_store = {}
        for i in range(num_baselines):
            if str(i) in validation_store:
                cleaned_store[str(i)] = validation_store[str(i)]
        
        return cleaned_store

    # --- Identify Peaks API Callback ---
    @app.callback(
        [Output('peaks-api-status-predict', 'children'),
         Output('api-result-store-predict', 'data'),
         Output('peak-data-store-predict', 'data', allow_duplicate=True),
         Output('global-params-table-predict', 'children'),
         Output('integration-events-table-predict', 'children')],
        Input('identify-peaks-predict', 'n_clicks'),
        [State('stored-sample-data-predict', 'data'),
         State('integration-range-start-predict', 'value'),
         State('integration-range-end-predict', 'value'),
         State('min-area-percent-predict', 'value'),
         State('long-baselines-store-predict', 'data')],
        prevent_initial_call=True
    )
    def identify_peaks_from_api(n_clicks, sample_data, int_start, int_end, min_area_percent, baselines_data):
        if not n_clicks or not sample_data:
            return "Error: No sample data", dash.no_update, dash.no_update, dash.no_update, dash.no_update

        try:
            # Show loading message immediately
            print("Starting peak identification...")
            
            # Validate sample data format
            if not isinstance(sample_data, dict) or 'time_steps' not in sample_data or 'signal' not in sample_data:
                return "Error: Invalid sample data format", dash.no_update, dash.no_update, dash.no_update, dash.no_update

            # Enhanced validation for integration range - check for empty/None values
            if int_start is None or int_start == "" or int_end is None or int_end == "":
                return "Error: Integration Range start and end times must be filled before identifying peaks", dash.no_update, dash.no_update, dash.no_update, dash.no_update
                
            try:
                integration_start = float(int_start)
                integration_end = float(int_end)
            except (ValueError, TypeError):
                return "Error: Integration range values must be valid numbers", dash.no_update, dash.no_update, dash.no_update, dash.no_update
                
            if integration_end <= integration_start:
                return "Error: Integration end time must be greater than start time", dash.no_update, dash.no_update, dash.no_update, dash.no_update

            # Process baselines data
            processed_baselines = []
            if baselines_data:
                for baseline in baselines_data:
                    if isinstance(baseline, dict):
                        processed_baselines.append({
                            "start": baseline.get("start", ""),
                            "end": baseline.get("end", "")
                        })

            print(f"Calling API with integration range: {integration_start} - {integration_end}")
            print(f"Baselines: {len(processed_baselines)}")

            # Call the mock API
            result, api_payload, integration_params = process_identify_peaks_api(
                chromatogram_data=sample_data,
                integration_start=integration_start,
                integration_end=integration_end,
                baselines=processed_baselines,
                min_area_percent=min_area_percent if min_area_percent is not None else 0.05
            )

            if result is None:
                return "Error: Peak identification failed. Make sure the mock API server is running.", dash.no_update, dash.no_update, dash.no_update, dash.no_update

            # Process the result for UI display
            if isinstance(result, list):
                peaks_data = result
            else:
                # Handle unexpected result format
                peaks_data = []

            # Normalize peaks data to ensure consistent format
            normalized_peaks = []
            for peak in peaks_data:
                peak_entry = {
                    "Name": peak.get("Name", "Unknown"),
                    "IntType": peak.get("IntType", "BB"),
                    "RetentionTime": float(peak.get("RetentionTime", 0.0)),
                    "StartTime": float(peak.get("StartTime", 0.0)),
                    "EndTime": float(peak.get("EndTime", 0.0)),
                    "Slope": float(peak.get("Slope", 0.0)) if peak.get("Slope") is not None else "",
                    "Offset": float(peak.get("Offset", 0.0)) if peak.get("Offset") is not None else "",
                    "Comment": ""  # Initialize empty comment
                }
                normalized_peaks.append(peak_entry)

            # Sort by retention time
            normalized_peaks = sorted(normalized_peaks, key=lambda x: x.get('RetentionTime', 0))

            # Create success message
            num_peaks = len(normalized_peaks)
            success_message = f"Successfully identified {num_peaks} peaks"

            # Save to database AFTER UI data is prepared
            peakseg_id = None
            try:
                # Wrap peaks list in a dictionary for database storage
                db_result = {"peaks": normalized_peaks, "integration_params": integration_params}
                peakseg_id = save_peakseg_to_db(
                    payload=api_payload,
                    result=db_result,  # Save the wrapped peaks list and integration params
                    request_submitter="ui_user"
                )
                
                if peakseg_id:
                    print(f"Successfully saved peakseg to database with ID: {peakseg_id}")
                else:
                    print("Warning: Failed to save peakseg to database")
            except Exception as e:
                print(f"Error saving peakseg to database: {e}")
                # Continue execution even if database save fails

            # Store the result for potential database saving or other uses
            api_result = {"peaks": normalized_peaks, "mock_api": True, "integration_params": integration_params, "peakseg_id": peakseg_id}

            print(f"Peak identification completed. Found {num_peaks} peaks.")
            print("Peaks DataFrame:")
            peaks_df = pd.DataFrame(normalized_peaks)
            print(peaks_df)
            print(f"Peaks DataFrame columns: {list(peaks_df.columns)}")
            print(f"Peaks DataFrame dtypes: {peaks_df.dtypes}")

            # Create Global Parameters table
            global_params_table = dash.no_update
            integration_events_table = dash.no_update
            
            if integration_params:
                # Table 1: Global Parameters
                global_data = [
                    {"Parameter": "Peak Width", "Value": integration_params.get('peak_width', 'N/A')},
                    {"Parameter": "Detection Threshold", "Value": integration_params.get('detection_threshold', 'N/A')},
                    {"Parameter": "Liftoff", "Value": integration_params.get('liftoff', 'N/A')},
                    {"Parameter": "Touchdown", "Value": integration_params.get('touchdown', 'N/A')},
                    {"Parameter": "Min Area", "Value": f"{integration_params.get('min_area', 'N/A'):.3f}" if isinstance(integration_params.get('min_area'), (int, float)) else integration_params.get('min_area', 'N/A')},
                    {"Parameter": "Min Height", "Value": integration_params.get('min_height', 'N/A')},
                    {"Parameter": "Integration Events", "Value": len(integration_params.get('integration_events', []))}
                ]
                
                global_params_table = dash_table.DataTable(
                    columns=[
                        {"name": "Parameter", "id": "Parameter"},
                        {"name": "Value", "id": "Value"}
                    ],
                    data=global_data,
                    style_table={'width': '100%'},
                    style_cell={'textAlign': 'left', 'padding': '8px'},
                    style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'},
                    style_data_conditional=[{'if': {'row_index': 'odd'}, 'backgroundColor': 'rgb(248, 248, 248)'}]
                )
                
                # Table 2: Integration Events (columns: Time (min), Type, Value, Stop (min))
                events_data = integration_params.get('integration_events', [])
                if events_data:
                    integration_events_table = dash_table.DataTable(
                        columns=[
                            {"name": "Time (min)", "id": "Time (min)", "type": "numeric", "format": {"specifier": ".3f"}},
                            {"name": "Type", "id": "Type"},
                            {"name": "Value", "id": "Value", "type": "numeric", "format": {"specifier": ".1f"}},
                            {"name": "Stop (min)", "id": "Stop (min)", "type": "numeric", "format": {"specifier": ".3f"}}
                        ],
                        data=events_data,
                        style_table={'width': '100%'},
                        style_cell={'textAlign': 'left', 'padding': '8px'},
                        style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'},
                        style_data_conditional=[{'if': {'row_index': 'odd'}, 'backgroundColor': 'rgb(248, 248, 248)'}]
                    )
                else:
                    integration_events_table = html.Div("No integration events", style={'padding': '10px', 'fontStyle': 'italic'})

            return success_message, api_result, normalized_peaks, global_params_table, integration_events_table

        except Exception as e:
            print(f"Error identifying peaks: {e}")
            import traceback
            traceback.print_exc()
            return f"Error: {str(e)}", dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # --- Loading State for Identify Peaks Button ---
    @app.callback(
        Output('peaks-loading-predict', 'style'),
        [Input('identify-peaks-predict', 'n_clicks'),
         Input('peaks-api-status-predict', 'children')],
        prevent_initial_call=True
    )
    def show_loading_state(n_clicks, status_message):
        ctx = dash.callback_context
        if not ctx.triggered:
            return {'display': 'none'}
            
        trigger = ctx.triggered[0]['prop_id'].split('.')[0]
        
        # Show loading when button is clicked
        if trigger == 'identify-peaks-predict' and n_clicks:
            return {'display': 'block'}
        
        # Hide loading when status message is updated (API completed)
        elif trigger == 'peaks-api-status-predict':
            return {'display': 'none'}
            
        return {'display': 'none'}

    # --- Direct Zoom Callback for Box Selection ---
    @app.callback(
        Output('chromatogram-plot-predict', 'figure', allow_duplicate=True),
        [Input('chromatogram-plot-predict', 'selectedData')],
        [State('chromatogram-plot-predict', 'figure')],
        prevent_initial_call=True
    )
    def direct_zoom_predict(selectedData, figure):
        """Handle box selection to zoom into selected area."""
        if not selectedData or 'range' not in selectedData:
            return dash.no_update
        x_range = selectedData['range']['x']
        y_range = selectedData['range']['y']
        figure['layout']['xaxis']['range'] = x_range
        figure['layout']['yaxis']['range'] = y_range
        figure['layout']['xaxis']['autorange'] = False
        figure['layout']['yaxis']['autorange'] = False
        return figure

    # Store selection data
    @app.callback(
        Output('selected-data-predict', 'children'),
        [Input('chromatogram-plot-predict', 'selectedData')],
        prevent_initial_call=True
    )
    def store_selection_predict(selectedData):
        """Store selection data for potential future use."""
        if selectedData:
            return json.dumps(selectedData)
        return dash.no_update 

    # --- Handle peaks table changes ---
    @app.callback(
        Output('peak-data-store-predict', 'data', allow_duplicate=True),
        [Input('peaks-data-table-predict', 'data')],
        prevent_initial_call=True
    )
    def handle_peaks_table_changes_predict(stored_data):
        if stored_data is not None:
            # When a row is deleted, the data is passed here.
            # We just need to update the store with this new data.
            # Remove the 'Row' field that was added for display
            updated_data = []
            for row in stored_data:
                new_row = {k: v for k, v in row.items() if k != 'Row'}
                updated_data.append(new_row)
            
            # Return None instead of an empty list when all peaks are deleted
            # This ensures the peak traces are properly cleared
            if len(updated_data) == 0:
                return None
                
            return updated_data
            
        return dash.no_update

    # --- Store integration parameters for download/copy ---
    @app.callback(
        [Output('global-params-store-predict', 'data'),
         Output('integration-events-store-predict', 'data')],
        [Input('api-result-store-predict', 'data')],
        prevent_initial_call=True
    )
    def store_integration_params_predict(api_result):
        global_data = []
        events_data = []
        
        # Extract data from API result if available
        if api_result and 'integration_params' in api_result:
            integration_params = api_result['integration_params']
            
            # Create global parameters data
            global_data = [
                {"Parameter": "Peak Width", "Value": integration_params.get('peak_width', 'N/A')},
                {"Parameter": "Detection Threshold", "Value": integration_params.get('detection_threshold', 'N/A')},
                {"Parameter": "Liftoff", "Value": integration_params.get('liftoff', 'N/A')},
                {"Parameter": "Touchdown", "Value": integration_params.get('touchdown', 'N/A')},
                {"Parameter": "Min Area", "Value": f"{integration_params.get('min_area', 'N/A'):.3f}" if isinstance(integration_params.get('min_area'), (int, float)) else integration_params.get('min_area', 'N/A')},
                {"Parameter": "Min Height", "Value": integration_params.get('min_height', 'N/A')},
                {"Parameter": "Integration Events", "Value": len(integration_params.get('integration_events', []))}
            ]
            
            # Get integration events data
            events_data = integration_params.get('integration_events', [])
            
        return global_data, events_data

    # --- Download Global Parameters CSV ---
    @app.callback(
        Output("download-global-params-csv-predict", "data"),
        [Input("download-global-params-btn-predict", "n_clicks")],
        [State("global-params-store-predict", "data")],
        prevent_initial_call=True
    )
    def download_global_params_csv_predict(n_clicks, stored_data):
        if not stored_data:
            return dash.no_update
        df = pd.DataFrame(stored_data)
        filename = f"Global_Parameters_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return dcc.send_data_frame(df.to_csv, filename, index=False)

    # --- Download Integration Events CSV ---
    @app.callback(
        Output("download-integration-events-csv-predict", "data"),
        [Input("download-integration-events-btn-predict", "n_clicks")],
        [State("integration-events-store-predict", "data")],
        prevent_initial_call=True
    )
    def download_integration_events_csv_predict(n_clicks, stored_data):
        if not stored_data:
            return dash.no_update
        df = pd.DataFrame(stored_data)
        filename = f"Integration_Events_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return dcc.send_data_frame(df.to_csv, filename, index=False)

    # --- Copy Global Parameters to Clipboard (Client-side) ---
    app.clientside_callback(
        """
        function(n_clicks, stored_data) {
            if (!n_clicks || !stored_data || stored_data.length === 0) {
                return "";
            }
            
            // Convert data to CSV format
            let csvContent = "Parameter,Value\\n";
            stored_data.forEach(row => {
                csvContent += `"${row.Parameter}","${row.Value}"\\n`;
            });
            
            // Copy to clipboard
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(csvContent).then(() => {
                    console.log('Global parameters copied to clipboard');
                }).catch(err => {
                    console.error('Failed to copy: ', err);
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement("textarea");
                textArea.value = csvContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }
            
            return "Global parameters copied to clipboard!";
        }
        """,
        Output('copy-global-params-feedback-predict', 'children'),
        [Input('copy-global-params-btn-predict', 'n_clicks')],
        [State('global-params-store-predict', 'data')],
        prevent_initial_call=True
    )

    # --- Copy Integration Events to Clipboard (Client-side) ---
    app.clientside_callback(
        """
        function(n_clicks, stored_data) {
            if (!n_clicks || !stored_data || stored_data.length === 0) {
                return "";
            }
            
            // Convert data to CSV format
            let csvContent = "Time (min),Type,Value,Stop (min)\\n";
            for (let i = 0; i < stored_data.length; i++) {
                let row = stored_data[i];
                let timeMin = row["Time (min)"] || "";
                let type = row["Type"] || "";
                let value = row["Value"] || "";
                let stopMin = row["Stop (min)"] || "";
                csvContent += timeMin + "," + type + "," + value + "," + stopMin + "\\n";
            }
            
            // Copy to clipboard
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(csvContent).then(() => {
                    console.log('Integration events copied to clipboard');
                }).catch(err => {
                    console.error('Failed to copy: ', err);
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement("textarea");
                textArea.value = csvContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }
            
            return "Integration events copied to clipboard!";
        }
        """,
        Output('copy-integration-events-feedback-predict', 'children'),
        [Input('copy-integration-events-btn-predict', 'n_clicks')],
        [State('integration-events-store-predict', 'data')],
        prevent_initial_call=True
    )

    # --- Clear copy feedback messages after delay using intervals ---
    @app.callback(
        Output('copy-global-params-feedback-predict', 'children', allow_duplicate=True),
        [Input('copy-feedback-interval-predict', 'n_intervals')],
        [State('copy-global-params-feedback-predict', 'children')],
        prevent_initial_call=True
    )
    def clear_global_params_feedback_predict(n_intervals, message):
        if message and message != "":
            return ""
        return dash.no_update

    @app.callback(
        Output('copy-integration-events-feedback-predict', 'children', allow_duplicate=True),
        [Input('copy-feedback-interval-predict', 'n_intervals')],
        [State('copy-integration-events-feedback-predict', 'children')],
        prevent_initial_call=True
    )
    def clear_integration_events_feedback_predict(n_intervals, message):
        if message and message != "":
            return ""
        return dash.no_update

    # Peakseg feedback save callback
    @app.callback(
        Output('feedback-save-status-predict', 'children'),
        Input('peaks-data-table-predict', 'data'),
        [State('api-result-store-predict', 'data'),
         State('peaks-data-table-predict', 'data_previous')],
        prevent_initial_call=True
    )
    def save_peakseg_feedback(current_data, api_result, previous_data):
        if not current_data or current_data == previous_data:
            return dash.no_update
        try:
            peakseg_id = None
            if isinstance(api_result, dict):
                peakseg_id = api_result.get("peakseg_id")
            if not peakseg_id:
                return dash.no_update
            
            # Import the feedback save function
            from utils import save_peakseg_feedback
            
            # Save the current table data as feedback
            success = save_peakseg_feedback(peakseg_id, current_data, "manual_edit")
            if success:
                return html.Div("✓ Feedback saved", style={'color': 'green', 'fontSize': '12px'})
            else:
                return html.Div("⚠ Failed to save feedback", style={'color': 'orange', 'fontSize': '12px'})
        except Exception as e:
            print(f"Error in feedback callback: {e}")
            return dash.no_update