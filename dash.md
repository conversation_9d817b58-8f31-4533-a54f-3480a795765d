CaTITLE: Accessing Callback Context in Dash
DESCRIPTION: Code snippet demonstrating how to access callback context to determine which inputs triggered a callback and access input/state values by name.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_26

LANGUAGE: python
CODE:
```
# Determine which inputs triggered the callback
triggered_inputs = dash.callback_context.triggered

# Access input/state values by name
button_clicks = dash.callback_context.states.get('btn.n_clicks')
```

----------------------------------------

TITLE: Implementing Dash Callbacks with Auto-generated IDs
DESCRIPTION: Demonstrates the new callback syntax that allows using component instances directly instead of ID strings, with auto-generated IDs if not supplied explicitly.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_11

LANGUAGE: python
CODE:
```
my_input = dcc.Input()
my_output = html.Div()
app.layout = html.Div([my_input, my_output])

@dash.callback(Output(my_output, 'children'), Input(my_input, 'value'))
def update(value):
    return f'You have entered {value}'
```

----------------------------------------

TITLE: Using Patch Operations for Efficient Updates
DESCRIPTION: Shows how to use patch operators to efficiently update portions of larger data structures without sending the entire object. This is particularly useful for optimizing performance when working with large datasets.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_5

LANGUAGE: Python
CODE:
```
from dash import Patch

@app.callback(
    Output('graph-data', 'figure'),
    Input('update-button', 'n_clicks')
)
def update_graph_partially(n_clicks):
    if not n_clicks:
        return dash.no_update
    
    # Create a Patch object
    patched_figure = Patch()
    
    # Update only specific parts of the figure
    patched_figure['data'][0]['y'] = [new_values]
    patched_figure['layout']['title'] = 'Updated Title'
    
    return patched_figure
```

----------------------------------------

TITLE: Setting Component Properties with set_props in Callbacks
DESCRIPTION: Demonstrates how to use set_props to update component properties during callback execution. This allows for updating multiple components, even those not defined in the callback's Output.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_4

LANGUAGE: Python
CODE:
```
from dash import callback_context

@app.callback(
    Output('output-id', 'children'),
    Input('input-id', 'value')
)
def update_output(value):
    # Update a component not in the Output
    callback_context.set_props({
        'another-component-id': {'property': 'new value'}
    })
    
    # Can be called multiple times
    callback_context.set_props({
        'yet-another-id': {'property': 'another value'}
    })
    
    return f"Input value: {value}"
```

----------------------------------------

TITLE: Using the Error Handler for Dash Callbacks
DESCRIPTION: Demonstrates how to implement error handling for Dash callbacks. The error handler can be defined globally or per callback and receives the exception as its first argument. It can return output values or None for no_update.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_2

LANGUAGE: Python
CODE:
```
# Global error handler on app initialization
app = Dash(__name__, on_callback_error=global_error_handler)

# Per-callback error handler
@app.callback(
    Output('output-id', 'children'),
    Input('input-id', 'value'),
    on_error=callback_specific_error_handler
)
def update_output(value):
    # function body
    pass

# Error handler function receives the exception
def error_handler(error):
    # Can return output values or None for no_update
    # Access to original callback context is preserved
    # set_props works inside the error handler
    return f"An error occurred: {error}"
```

----------------------------------------

TITLE: Defining Dash Callbacks
DESCRIPTION: Adds interactivity to Dash applications using both server-side and client-side callbacks. Server-side callbacks use Python functions while client-side callbacks use JavaScript. Both types connect inputs and outputs through component properties.
SOURCE: https://github.com/plotly/dash/blob/dev/MAKE_A_NEW_BACK_END.md#2025-04-21_snippet_4

LANGUAGE: python
CODE:
```
@app.callback(Output(...), Input(...), Input(...), State(...), ...)
def my_callback(input1, input2, state):
    <do stuff>
    return my_output_value

app.clientside_callback(
    "<JavaScript function as a string or reference>",
    Output(...), Input(...), Input(...), State(...), ...
)
```

----------------------------------------

TITLE: Implementing Dash Hooks in Python
DESCRIPTION: Shows how to use the new hook-based extension system for Dash. These hooks allow packages to extend Dash functionality through entry points and provide ways to modify layouts, add callbacks, and customize app behavior.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_1

LANGUAGE: Python
CODE:
```
# In setup.py - define entry points
entry_points={
    'dash-hooks': ['module_name = package.module']
}

# Using hooks in Python
dash.hooks.layout  # takes and returns a layout
dash.hooks.callback  # defines a callback
dash.hooks.clientside_callback  # defines a clientside callback
dash.hooks.setup  # called before app start with app instance
dash.hooks.error  # receives callback errors
dash.hooks.index  # changes Dash.index_string
dash.hooks.route  # adds a Flask route
```

----------------------------------------

TITLE: Accessing Callback Context in Clientside Callbacks
DESCRIPTION: Shows how to access the callback context in clientside callbacks. The window.dash_clientside.callback_context object provides information about the callback's outputs, inputs, and state.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_3

LANGUAGE: JavaScript
CODE:
```
window.dash_clientside.my_namespace = {
    my_clientside_function: function(input_value) {
        // Access the callback context
        const context = window.dash_clientside.callback_context;
        
        // Access outputs_list property
        const outputs = context.outputs_list;
        
        // Other available properties
        const triggered = context.triggered;
        const inputs = context.inputs;
        const states = context.states;
        
        return some_result;
    }
};
```

----------------------------------------

TITLE: Accessing Redux Store via dash_component_api Methods
DESCRIPTION: Shows how to access the Dash app's Redux store using the dash_component_api methods. These methods provide alternatives to removed _dashprivate props and allow components to interact with the Dash context.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_0

LANGUAGE: JavaScript
CODE:
```
dash_component_api.useDashContext
dash_component_api.DashContext

// Context properties and methods
DashContext.componentPath
DashContext.useLoading(options?)
DashContext.isLoading
DashContext.useSelector
DashContext.useStore
DashContext.useDispatch

// Layout access method
dash_component_api.getLayout(path)
```

----------------------------------------

TITLE: Creating an Interactive DataTable with Dash and Pandas
DESCRIPTION: This snippet demonstrates how to create a basic interactive DataTable using Dash and pandas. It loads data from a CSV file, creates a Dash application, and renders the data in a DataTable component.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-table/README.md#2025-04-21_snippet_1

LANGUAGE: python
CODE:
```
from dash import Dash, dash_table
import pandas as pd

df = pd.read_csv('https://raw.githubusercontent.com/plotly/datasets/master/solar.csv')

app = Dash(__name__)

app.layout = dash_table.DataTable(
    id='table',
    columns=[{"name": i, "id": i} for i in df.columns],
    data=df.to_dict('records'),
)

if __name__ == '__main__':
    app.run(debug=True)
```

----------------------------------------

TITLE: Enabling MathJax Support in Dash Components
DESCRIPTION: Example of enabling MathJax support for math rendering in dcc.Markdown and dcc.Graph components. MathJax can be enabled by setting the mathjax prop to True, allowing for LaTeX math notation rendering.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_6

LANGUAGE: python
CODE:
```
# For dcc.Markdown component
dcc.Markdown("$E=mc^2$", mathjax=True)

# For dcc.Graph component
dcc.Graph(figure=fig, mathjax=True)
```

----------------------------------------

TITLE: Simplified DataTable Implementation
DESCRIPTION: Shows the new simplified DataTable syntax with automatic column inference from data.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_15

LANGUAGE: python
CODE:
```
dash_table.DataTable(data=df.to_dict('records'))
```

----------------------------------------

TITLE: Simplified Dropdown Component Usage
DESCRIPTION: Shows the new simplified syntax for Dropdown components with flexible types for options and values.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_13

LANGUAGE: python
CODE:
```
dcc.Dropdown(['New York', 'Montreal'], 'New York')
```

LANGUAGE: python
CODE:
```
dcc.Dropdown({'NYC': 'New York', 'MTL': 'Montreal'}, 'New York')
```

----------------------------------------

TITLE: Using Data and ARIA Attributes in Dash HTML Components
DESCRIPTION: Demonstrates how to add aria-* and data-* attributes to Dash HTML components using dictionary expansion syntax.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_30

LANGUAGE: python
CODE:
```
html.Div(id="my-div", **{"data-toggle": "toggled", "aria-toggled": "true"})
```

----------------------------------------

TITLE: Using Walrus Operator with Dash Callbacks
DESCRIPTION: Shows how to use Python 3.8's walrus operator (:=) to define and reference components inline within the layout definition.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_12

LANGUAGE: python
CODE:
```
app.layout = html.Div([
    my_input := dcc.Input(),
    my_output := html.Div()
])

@dash.callback(Output(my_output, 'children'), Input(my_input, 'value'))
def update(value):
    return f'You have entered {value}'
```

----------------------------------------

TITLE: Modifying Flask Response with Custom Headers
DESCRIPTION: Code snippet showing how to add custom cookies or headers to the Flask response object using the callback_context.response property in Dash.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_27

LANGUAGE: python
CODE:
```
# Adding custom cookies or headers to the response
dash.callback_context.response
```

----------------------------------------

TITLE: Specifying Python Package Dependencies
DESCRIPTION: Defines required Python packages and version constraints needed to run a Dash/Plotly application. Includes core web framework dependencies (Flask, Werkzeug), visualization library (Plotly), and utility packages for Python compatibility and functionality.
SOURCE: https://github.com/plotly/dash/blob/dev/requirements/install.txt#2025-04-21_snippet_0

LANGUAGE: plain text
CODE:
```
Flask>=1.0.4,<3.1
Werkzeug<3.1
plotly>=5.0.0
importlib-metadata
typing_extensions>=4.1.1
requests
retrying
nest-asyncio
setuptools
```

----------------------------------------

TITLE: Demonstrating PreventUpdate in Dash Clientside Functions
DESCRIPTION: Shows how to prevent updates in clientside functions by throwing a PreventUpdate exception or returning a no_update value.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_19

LANGUAGE: javascript
CODE:
```
throw window.dash_clientside.PreventUpdate;
```

LANGUAGE: javascript
CODE:
```
return window.dash_clientside.no_update
```

----------------------------------------

TITLE: Running the Dash Server
DESCRIPTION: Starts the web server that hosts the Dash application. The run_server method accepts various options that control how the server operates, including development tools and performance settings.
SOURCE: https://github.com/plotly/dash/blob/dev/MAKE_A_NEW_BACK_END.md#2025-04-21_snippet_5

LANGUAGE: python
CODE:
```
app.run_server(...)
```

----------------------------------------

TITLE: Enabling Persistence in Dash Components
DESCRIPTION: Demonstrates how to enable prop persistence in Dash components by setting the persistence, persisted_props, and persistence_type props.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_22

LANGUAGE: python
CODE:
```
persistence=True
```

----------------------------------------

TITLE: Enabling Undo/Redo Toolbar in Dash
DESCRIPTION: Code snippet showing how to enable the undo/redo toolbar functionality when initializing a Dash application.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_25

LANGUAGE: python
CODE:
```
app=Dash(show_undo_redo=true)
```

----------------------------------------

TITLE: Running Pytest for Dash Tests
DESCRIPTION: This snippet demonstrates how to run Pytest for Dash tests, including running all tests, specific directories, or filtering by test case name.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_11

LANGUAGE: bash
CODE:
```
npm run test
pytest tests/unit
pytest -k cbcx004
pytest -k cbcx
```

----------------------------------------

TITLE: Relative Path Functions in Dash
DESCRIPTION: New utility functions added to Dash for handling relative paths and asset URLs without requiring direct app object access.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_10

LANGUAGE: python
CODE:
```
dash.get_relative_path
dash.strip_relative_path
dash.get_asset_url
```

----------------------------------------

TITLE: Specifying Dependencies for DiskcacheLongCallbackManager in Dash
DESCRIPTION: This snippet specifies the minimum required versions of three Python packages needed by the DiskcacheLongCallbackManager in Dash: diskcache, multiprocess, and psutil. These packages are essential for handling disk-based caching, multiprocessing, and system utilization monitoring respectively.
SOURCE: https://github.com/plotly/dash/blob/dev/requirements/diskcache.txt#2025-04-21_snippet_0

LANGUAGE: plaintext
CODE:
```
diskcache>=5.2.1
multiprocess>=0.70.12
psutil>=5.8.0
```

----------------------------------------

TITLE: Using new Download component in Dash Core Components (Python)
DESCRIPTION: Example of using the new Download component and utility functions for sending files, DataFrames, bytes and strings.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_17

LANGUAGE: Python
CODE:
```
import dash_core_components as dcc

dcc.Download(id='download')

# In a callback:
dcc.send_file('/path/to/file')
dcc.send_data_frame(df.to_csv, 'myfile.csv')
dcc.send_bytes(b'content', 'myfile.txt')
dcc.send_string('content', 'myfile.txt')
```

----------------------------------------

TITLE: Defining Dependencies for Dash CeleryLongCallbackManager
DESCRIPTION: Specifies the minimum version requirements for Redis and Celery packages needed by the CeleryLongCallbackManager in Dash. Redis 3.5.3 or newer is required as a message broker, and Celery 5.1.2 or newer with Redis integration is needed for task processing.
SOURCE: https://github.com/plotly/dash/blob/dev/requirements/celery.txt#2025-04-21_snippet_0

LANGUAGE: plaintext
CODE:
```
redis>=3.5.3
celery[redis]>=5.1.2
```

----------------------------------------

TITLE: Simplified Slider Component Implementation
DESCRIPTION: Demonstrates the new simplified syntax for Slider components with automatic step calculation and mark generation.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_14

LANGUAGE: python
CODE:
```
dcc.Slider(1, 3, 1)
```

LANGUAGE: python
CODE:
```
dcc.Slider(0, 100)
```

----------------------------------------

TITLE: Creating Textarea Component in Dash
DESCRIPTION: Implementation of the new Textarea component in Dash for displaying multiline text content. The component's value can be controlled through the value property.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-core-components/CHANGELOG.md#2025-04-21_snippet_3

LANGUAGE: python
CODE:
```
dcc.Textarea(id='my-text-area' value='''
SELECT * FROM MY_TABLES
LIMIT 10;
''')
```

----------------------------------------

TITLE: Using Improved Callback Context in Dash 2.4.0+
DESCRIPTION: Examples of using the improved callback context features added in Dash 2.4.0, including the more concise dash.ctx name and new properties like triggered_id and args_grouping for accessing callback information.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_8

LANGUAGE: python
CODE:
```
# Using the more concise dash.ctx instead of dash.callback_context
from dash import ctx

# Getting the id of the component that triggered the callback
triggered_id = ctx.triggered_id

# Getting a dictionary of the component ids and props that triggered the callback
triggered_props = ctx.triggered_prop_ids

# Accessing grouped arguments when using flexible callback signatures
args = ctx.args_grouping
```

----------------------------------------

TITLE: Disabling Local Assets Serving in Dash
DESCRIPTION: Code snippet demonstrating how to configure Dash to serve JavaScript assets from CDN instead of locally by setting the serve_locally property to False.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_24

LANGUAGE: python
CODE:
```
app.scripts.config.serve_locally = False
```

----------------------------------------

TITLE: Enabling Dev Tools in Dash Application
DESCRIPTION: Code snippet showing how to enable the Dev Tools UI and property validation in a Dash application using configuration flags in the run_server method.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_23

LANGUAGE: python
CODE:
```
app.run_server(dev_tools_props_check=True, dev_tools_ui=True)
```

----------------------------------------

TITLE: Using disabled_days prop in DatePicker components (JavaScript)
DESCRIPTION: New 'disabled_days' prop for DatePickerRange and DatePickerSingle to specify unselectable days.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_18

LANGUAGE: JavaScript
CODE:
```
<DatePickerRange
  disabled_days={[new Date(2021, 3, 1), new Date(2021, 3, 2)]}
/>
```

----------------------------------------

TITLE: Setting Dash Application Layout
DESCRIPTION: Defines the UI structure of the Dash application using nested Dash components. The layout property determines what will be displayed in the browser.
SOURCE: https://github.com/plotly/dash/blob/dev/MAKE_A_NEW_BACK_END.md#2025-04-21_snippet_3

LANGUAGE: python
CODE:
```
app.layout = html.Div(...)
```

----------------------------------------

TITLE: Initializing a Dash Application in Python
DESCRIPTION: Creates a new Dash application instance. The constructor accepts various configuration options that control the behavior and features of the application.
SOURCE: https://github.com/plotly/dash/blob/dev/MAKE_A_NEW_BACK_END.md#2025-04-21_snippet_2

LANGUAGE: python
CODE:
```
from dash import Dash
app = Dash(...)
```

----------------------------------------

TITLE: Using External Plotly.js Script
DESCRIPTION: Example URL for including strict bundle of Plotly.js as an external script in Dash for CSP compliance.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_9

LANGUAGE: javascript
CODE:
```
https://cdn.plot.ly/plotly-strict-2.11.0.min.js
```

----------------------------------------

TITLE: Disabling Default Viewport Meta Tag in Dash
DESCRIPTION: Example of how to disable the default viewport meta tag that was added in Dash 2.5.0. This code demonstrates providing an empty viewport meta tag to the Dash application constructor.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_7

LANGUAGE: python
CODE:
```
app = Dash(meta_tags=[{"name": "viewport"}])
```

----------------------------------------

TITLE: Specifying Dash CI Dependencies with Version Requirements
DESCRIPTION: A comprehensive list of Python packages with their version constraints used for Continuous Integration testing of the Plotly Dash project. Includes testing tools, code quality checkers, and data processing libraries.
SOURCE: https://github.com/plotly/dash/blob/dev/requirements/ci.txt#2025-04-21_snippet_0

LANGUAGE: plain
CODE:
```
black==22.3.0
flake8==7.0.0
flaky==3.8.1
flask-talisman==1.0.0
ipython<9.0.0
mimesis<=11.1.0
mock==4.0.3
numpy<=1.26.3
orjson==3.10.3
openpyxl
pandas>=1.4.0
pyarrow
pylint==3.0.3
pytest-mock
pytest-sugar==0.9.6
pyzmq==25.1.2
xlrd>=2.0.1
pytest-rerunfailures
jupyterlab<4.0.0
pyright==1.1.398;python_version>="3.7"
```

----------------------------------------

TITLE: Configuring Graph Component in Python
DESCRIPTION: Example showing how to configure a Dash Graph component with custom mode bar buttons and editable properties
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-core-components/CHANGELOG.md#2025-04-21_snippet_2

LANGUAGE: python
CODE:
```
import dash
import dash_core_components as dcc
import dash_html_components as html

app = dash.Dash()

app.layout = html.Div([
    dcc.Graph(
        id='my-graph',
        figure={'data': [{'x': [1, 2, 3]}]},
        config={'editable': True, 'modeBarButtonsToRemove': ['pan2d', 'lasso2d']}
    )
])

if __name__ == '__main__':
    app.run_server(debug=True)
```

----------------------------------------

TITLE: Adding extra hot reload paths in Dash constructor (Python)
DESCRIPTION: New Dash constructor argument 'extra_hot_reload_paths' allows re-initializing Python code when non-Python files change.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_16

LANGUAGE: Python
CODE:
```
app = dash.Dash(__name__, extra_hot_reload_paths=['/path/to/non/python/file'])
```

----------------------------------------

TITLE: CSS Selectors for Styling Dash Table Components
DESCRIPTION: Demonstrates CSS selectors and classes available for styling dash-table components. Shows how to target specific columns and style various table elements using data attributes and predefined classes.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-table/CHANGELOG.md#2025-04-21_snippet_1

LANGUAGE: css
CODE:
```
.dash-cell,
.dash-header {
    &[data-dash-column='ticker'] {
        // styling
    }
}
```

----------------------------------------

TITLE: Implementing Percentage-Based Column Width in Dash Table
DESCRIPTION: Shows how to configure a dash-table to use percentage-based column widths by setting content_style to 'grow', defining table styles, and specifying percentage values for column width.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-table/CHANGELOG.md#2025-04-21_snippet_0

LANGUAGE: javascript
CODE:
```
content_style='grow'

table_style=[{ selector: '.dash-spreadsheet', rule: 'width: 100%; max-width: 100%' }]

columns=[{
    id: 'column',
    width: '40%'
}]
```

----------------------------------------

TITLE: Installing Dash Core Components for Development
DESCRIPTION: Instructions for installing Dash and its core components in development mode with extras. This process includes setting up a virtual environment, installing dependencies, and building the project.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-core-components/README.md#2025-04-21_snippet_0

LANGUAGE: bash
CODE:
```
# It's recommended to install your python packages in a virtualenv
# As of dash 2.0, python 3 is required
$ python -m venv venv && . venv/bin/activate

# make sure dash is installed with dev and testing dependencies
$ pip install -e .[dev,testing]  # in some shells you need \ to escape []

# run the build process - this will build all of dash, including dcc
$ npm ci && npm run build

# install dcc in editable mode
$ pip install -e .
```

----------------------------------------

TITLE: Dash HTML Template Structure in Python
DESCRIPTION: Basic structure of the HTML template used for rendering Dash applications. It includes placeholders for configuration, scripts, and renderer initialization.
SOURCE: https://github.com/plotly/dash/blob/dev/MAKE_A_NEW_BACK_END.md#2025-04-21_snippet_1

LANGUAGE: python
CODE:
```
"""<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>"""
```

----------------------------------------

TITLE: Setting React Version in Dash Renderer
DESCRIPTION: Code to configure which React version (15.4.2 or 16.2.0) to use with dash_renderer before initializing the Dash application.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_29

LANGUAGE: python
CODE:
```
import dash_renderer

# Set the react version before setting up the Dash application
dash_renderer._set_react_version('16.2.0')

app = dash.Dash(...)
```

----------------------------------------

TITLE: Serializing Dash Component to JSON in Python
DESCRIPTION: Example of how a Dash component should serialize to JSON when requested by the framework. This format is consistent across all backends.
SOURCE: https://github.com/plotly/dash/blob/dev/MAKE_A_NEW_BACK_END.md#2025-04-21_snippet_0

LANGUAGE: json
CODE:
```
{"namespace": "dash_core_components", "type": "Checklist", "props": {...}}
```

----------------------------------------

TITLE: Setting Working Directory for R Apps in Dash Testing
DESCRIPTION: Demonstrates how to set the working directory when running R apps using the dashr fixture in Dash testing.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_20

LANGUAGE: r
CODE:
```
dashr.start_server(cwd="/path/to/working/directory")
```

----------------------------------------

TITLE: Generating HTML Components for Dash
DESCRIPTION: Runs the npm script that programmatically generates components in src/components and the export index in src/index.js from element definitions.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_3

LANGUAGE: sh
CODE:
```
$ npm run generate-components
```

----------------------------------------

TITLE: Configuring Percy Assets Path in Pytest
DESCRIPTION: Shows how to specify extra application assets path for Percy in pytest configuration.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_21

LANGUAGE: bash
CODE:
```
--percy-assets
```

----------------------------------------

TITLE: Defining Dash Testing Dependencies in Requirements File
DESCRIPTION: A comprehensive list of Python package dependencies needed for testing Dash applications. The file specifies version constraints for packages like BeautifulSoup, Selenium, and other utilities required for browser automation, web scraping, security, and process management.
SOURCE: https://github.com/plotly/dash/blob/dev/requirements/testing.txt#2025-04-21_snippet_0

LANGUAGE: plaintext
CODE:
```
beautifulsoup4>=4.8.2
cryptography
lxml>=4.6.2
percy>=2.0.2
pytest>=6.0.2
requests[security]>=2.21.0
selenium>=3.141.0,<=4.2.0
waitress>=1.4.4
multiprocess>=0.70.12
psutil>=5.8.0
dash_testing_stub>=0.0.2
```

----------------------------------------

TITLE: Specifying Python Dependencies for Dash Component Development
DESCRIPTION: Lists the required Python packages with minimum version constraints needed for developing new Dash components. The dependencies include coloredlogs for enhanced logging output, fire for creating command-line interfaces, and PyYAML for processing YAML configuration files.
SOURCE: https://github.com/plotly/dash/blob/dev/requirements/dev.txt#2025-04-21_snippet_0

LANGUAGE: plaintext
CODE:
```
coloredlogs>=15.0.1
fire>=0.4.0
PyYAML>=5.4.1
```

----------------------------------------

TITLE: Running Selenium Integration Tests for Dash Core Components
DESCRIPTION: Command to execute the Selenium integration tests defined in test_integration.py for Dash Core Components.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-core-components/README.md#2025-04-21_snippet_1

LANGUAGE: bash
CODE:
```
npm test
```

----------------------------------------

TITLE: Building Dash Components - Linux/Mac
DESCRIPTION: Command to build all Dash components on Linux/Mac systems.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_6

LANGUAGE: bash
CODE:
```
npm run build
```

----------------------------------------

TITLE: Installing Dash Dependencies
DESCRIPTION: Commands to install Dash and its dependencies using pip and npm.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_5

LANGUAGE: bash
CODE:
```
pip install -e .[ci,dev,testing,celery,diskcache]
npm ci
```

----------------------------------------

TITLE: Setting Up Test Components
DESCRIPTION: Command to build and install components used in tests.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_9

LANGUAGE: bash
CODE:
```
npm run setup-tests.py
```

----------------------------------------

TITLE: Building Dash Components - Windows
DESCRIPTION: Command to build all Dash components on Windows systems with additional setup steps.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_7

LANGUAGE: bash
CODE:
```
npm run first-build
```

----------------------------------------

TITLE: Installing Python Package Locally
DESCRIPTION: Installs the Python package in the local site-packages directory for testing before publishing to PyPi.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_6

LANGUAGE: sh
CODE:
```
# Install in `site-packages` on your machine
$ npm run install-local
```

----------------------------------------

TITLE: Running Build Watcher for Dash Core Components
DESCRIPTION: Command to start the build watcher for continuous compilation of Dash Core Components during development.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-core-components/README.md#2025-04-21_snippet_2

LANGUAGE: bash
CODE:
```
npm run build:watch
```

----------------------------------------

TITLE: Building Dash HTML Components for Production
DESCRIPTION: Creates a production build of the JavaScript code using npm's build script.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_8

LANGUAGE: sh
CODE:
```
$ npm run build
```

----------------------------------------

TITLE: Updating Individual Components
DESCRIPTION: Command to rebuild specific Dash components during development.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_8

LANGUAGE: bash
CODE:
```
dash-update-components "dash-core-components"
```

----------------------------------------

TITLE: Creating a Python Distribution Package
DESCRIPTION: Generates a Python distribution tarball in the dist/ folder using setuptools.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_9

LANGUAGE: sh
CODE:
```
$ python setup.py sdist
```

----------------------------------------

TITLE: Verifying Dash Installation
DESCRIPTION: Commands to verify the successful installation of Dash.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_10

LANGUAGE: bash
CODE:
```
pip list | grep dash
```

----------------------------------------

TITLE: Publishing to PyPI
DESCRIPTION: Uploads the distribution package to the Python Package Index (PyPI) using twine.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_11

LANGUAGE: sh
CODE:
```
$ twine upload dist/*
```

----------------------------------------

TITLE: Watching for Changes During Development
DESCRIPTION: Starts a watch process that automatically rebuilds the JavaScript bundle when changes are detected in the source files.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_4

LANGUAGE: bash
CODE:
```
$ npm run build:watch
```

----------------------------------------

TITLE: Installing Module Locally for Development
DESCRIPTION: Installs the module locally for testing, generating metadata and building the JavaScript bundle in the process.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_5

LANGUAGE: bash
CODE:
```
# Generate metadata, and build the JavaScript bundle
$ npm run install-local

# Now you're done. For subsequent changes, if you've got `npm run build:watch`
$ python setup.py install
```

----------------------------------------

TITLE: Installing Dash via pip
DESCRIPTION: Command to install the Dash library using pip package manager.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-table/README.md#2025-04-21_snippet_0

LANGUAGE: bash
CODE:
```
pip install dash
```

----------------------------------------

TITLE: Testing the Distribution Package
DESCRIPTION: Tests the generated tarball by installing it in a new environment to verify it works properly.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_10

LANGUAGE: sh
CODE:
```
$ pip install dash-html-components-<new-version>.tar.gz
```

----------------------------------------

TITLE: Uninstalling Python Package Locally
DESCRIPTION: Removes the locally installed Python package when testing is complete.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_7

LANGUAGE: sh
CODE:
```
$ npm run uninstall-local
```

----------------------------------------

TITLE: Setting Up Python Virtual Environment - Windows
DESCRIPTION: Commands to create and activate a Python virtual environment on Windows systems.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_4

LANGUAGE: bash
CODE:
```
python -m venv .venv/dev
source .venv/dev/scripts/activate
```

----------------------------------------

TITLE: Specifying Flask-Compress Dependency
DESCRIPTION: Package name specification for Flask-Compress, a Flask extension that compresses responses in Flask applications using gzip or other compression algorithms.
SOURCE: https://github.com/plotly/dash/blob/dev/requirements/compress.txt#2025-04-21_snippet_0

LANGUAGE: txt
CODE:
```
flask-compress
```

----------------------------------------

TITLE: Example Version Entry Format in Markdown
DESCRIPTION: Shows the standard format used for version entries in the changelog, including version number, date, and categorized changes (Fixed, Updated, Added, Changed).
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-core-components/CHANGELOG.md#2025-04-21_snippet_0

LANGUAGE: markdown
CODE:
```
## [1.17.1] - 2021-07-12

### Fixed

- Removed unnecessary Julia files from npm package
```

----------------------------------------

TITLE: Issue Reference Format in Markdown
DESCRIPTION: Demonstrates how GitHub issues and pull requests are referenced in the changelog entries using markdown links.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-core-components/CHANGELOG.md#2025-04-21_snippet_1

LANGUAGE: markdown
CODE:
```
[#963](https://github.com/plotly/dash-core-components/pull/963) Fixes [#885](https://github.com/plotly/dash-core-components/issues/885)
```

----------------------------------------

TITLE: CSS Hack for Hiding Undo/Redo Toolbar (Deprecated)
DESCRIPTION: CSS hack to hide the undo/redo toolbar in previous versions of Dash, which is no longer needed with the show_undo_redo parameter.
SOURCE: https://github.com/plotly/dash/blob/dev/CHANGELOG.md#2025-04-21_snippet_28

LANGUAGE: css
CODE:
```
._dash-undo-redo:{display:none;}
```

----------------------------------------

TITLE: Setting Up Python Virtual Environment - Linux/Mac
DESCRIPTION: Commands to create and activate a Python virtual environment on Linux/Mac systems.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_3

LANGUAGE: bash
CODE:
```
python3 -m venv .venv/dev
source .venv/dev/bin/activate
```

----------------------------------------

TITLE: Setting up Virtual Environment for Dash HTML Components
DESCRIPTION: Creates and activates a virtual environment for development. Includes a note about Windows activation path differences.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_0

LANGUAGE: bash
CODE:
```
$ virtualenv venv
$ venv/bin/activate
```

----------------------------------------

TITLE: Installing Python Dependencies for Dash HTML Components
DESCRIPTION: Installs the required Python packages needed to build the components using pip and the dev-requirements.txt file.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_1

LANGUAGE: bash
CODE:
```
$ pip install -r dev-requirements.txt
```

----------------------------------------

TITLE: Installing npm Packages for Dash HTML Components
DESCRIPTION: Installs the required npm packages needed for component generation and development using the CI (clean install) command.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_2

LANGUAGE: bash
CODE:
```
$ npm ci
```

----------------------------------------

TITLE: Publishing to NPM
DESCRIPTION: Publishes the package to NPM to make JavaScript bundles available on the unpkg CDN.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_13

LANGUAGE: sh
CODE:
```
$ npm publish
```

----------------------------------------

TITLE: Installing Latest Node.js Version
DESCRIPTION: Commands to install and activate the latest version of Node.js using nvm (Node Version Manager).
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_2

LANGUAGE: bash
CODE:
```
nvm install latest
```

LANGUAGE: bash
CODE:
```
nvm use latest
```

LANGUAGE: bash
CODE:
```
node -v
npm -v
```

----------------------------------------

TITLE: Cloning Dash Repository - HTTPS Method
DESCRIPTION: Command to clone the forked Dash repository using HTTPS protocol. Replace <your_user_name> with your GitHub username.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_0

LANGUAGE: bash
CODE:
```
git clone https://github.com/<your_user_name>/dash.git
```

----------------------------------------

TITLE: Cloning Dash Repository - SSH Method
DESCRIPTION: Command to clone the forked Dash repository using SSH protocol. Replace <your_user_name> with your GitHub username.
SOURCE: https://github.com/plotly/dash/blob/dev/CONTRIBUTING.md#2025-04-21_snippet_1

LANGUAGE: bash
CODE:
```
<NAME_EMAIL>:<your_user_name>/dash.git
```

----------------------------------------

TITLE: Cleaning up the Distribution Folder
DESCRIPTION: Removes the distribution folder after publishing to clean up the workspace.
SOURCE: https://github.com/plotly/dash/blob/dev/components/dash-html-components/README.md#2025-04-21_snippet_12

LANGUAGE: sh
CODE:
```
$ rimraf dist
```