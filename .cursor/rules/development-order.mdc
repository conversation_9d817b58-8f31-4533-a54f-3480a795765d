---
description: 
globs: 
alwaysApply: false
---
I. Define a Clear API Contract

Establish a precise contract for the model's API before they finish development:
1. Endpoint URL: service name + port; or IP address
2. HTTP Method: POST
3. Request Format: Define the exact JSON structure the frontend will send, including how the chromatogram time-series data and the Integration Parameters  will be formatted.
4. Response Format: Define the structure of the successful response (e.g., JSON containing the Peak Table data or a link to an Excel file) and potential error responses (including status codes and error messages).


II. Implement Robust Frontend API Interaction

In the callbacks.py, add the logic to:
1. Gather the chromatogram sample data loaded, the current Integration Parameters and Integration Events from the frontend components
2. Format the data according to the API contract.
3. Send the data to the (initially mock, later real) backend API endpoint using a library like requests
4. Handle API responses: parse the Peak Table data, update the frontend table, and manage potential errors (network issues, timeouts, backend errors, invalid data). Display user-friendly error messages


III. Input Validation

Add validation logic in utils.py to check Integration Parameters and Integration Events data gathered from the frontend before sending them to the backend. Ensure values are within expected ranges or formats


IV. Deployment preparation

### Running with Docker

The application can be run using Docker for easy deployment and consistent environments.

#### Prerequisites
- Docker installed on your system
- Docker Compose installed on your system

#### Running with Docker Compose

1. Build and start the container:
   ```bash
   ./run_docker.sh
   ```
   Or manually with:
   ```bash
   docker-compose up --build -d
   ```

2. Access the application at http://localhost:8051

3. To stop the application:
   ```bash
   docker-compose down
   ```

#### Building and Running Manually

1. Build the Docker image:
   ```bash
   docker build -t chromatogram-viewer .
   ```

2. Run the container:
   ```bash
   docker run -p 8051:8050 chromatogram-viewer
   ```

### Kubernetes Deployment

1. Add basic health check endpoints to the Dash app (app.py); configure livenessProbe and readinessProbe in deployment.yaml to point to this endpoint
2. Azure TFS CI/CD Pipeline Configuration
3. Ensure the application logs relevant information (e.g., API requests/responses, errors) to standard output/error streams
4. Dash 作为一个 Python web 服务，如果需要支持更多并发量，可在 K8S 中通过副本数（Replica）进行横向扩容。
5. 配合负载均衡器（LoadBalancer/Ingress）以及健康检查探针（readinessProbe / livenessProbe）保证服务可用性。


V. How K8S routes external traffic for this project (Dash + FastAPI setup)

1. Separate Subdomains/Domains (Often Recommended)

Configure the Ingress controller to route traffic based on hostname:
app.yourdomain.com -> routes to the Dash app Service.
api.yourdomain.com -> routes to the FastAPI model Service.
	•	前端的 Service 叫 app-service，后端（模型）Service 叫 model-service，都在同一个命名空间或不同命名空间内。外部用户访问 app.xxx.com 就能打开前端页面，访问 api.xxx.com 就能调用后端 API。
    •	前后端的 Pod 都会自动注册到 Kubernetes DNS 中，Service 获得一个内网可解析的域名，例如：model-service.namespace.svc.cluster.local。
	•	K8S 集群内部，前端服务若要访问后端 API，可以直接用 http://model-service:端口（同 namespace 时）或 http://model-service.<namespace>.svc.cluster.local:端口（跨 namespace 时）等地址进行内部通信，这就走了内部网络（ClusterIP），流量不会绕到公网。

Simple and clear separation. If both services are deployed within the same Kubernetes cluster, in the same region/availability zone, and communicate via their internal K8S service names or ClusterIPs, the traffic usually stays within the Azure network.

But need to manage DNS for two subdomains (or domains).
如果你现在的前后端强依赖在同域名下避免跨域，那子域名方式会带来 CORS 问题(浏览器端会识别为跨域请求)，需要在后端或 Ingress 层允许 Access-Control-Allow-Origin。

2. Same Domain with Path-Based Routing (Possible but Complex)

Deploy both services as separate Services. Configure the Ingress controller for path-based routing:
yourdomain.com/ -> routes to the Dash app Service.
yourdomain.com/api/ -> routes to the FastAPI model Service.

单域名 + Path-based Routing:
如果你想“少折腾”，可以尝试让后端（FastAPI）直接跑在子路径 /api 上，不做 rewrite。只要 Ingress 指定把 /api 路径流量导向后端服务就行，相对简单一些。
后端是否监听 /api 路径?

Single domain entry point for users/clients.

But:
- Ingress Complexity: Requires more careful Ingress configuration. You often need path rewriting.
- Both your Dash app and FastAPI service might need to be configured to correctly handle being served under a specific path prefix (/ for Dash, /api/ for FastAPI), affecting how they generate internal links or URLs. Dash uses requests_pathname_prefix, and FastAPI has root_path. This adds configuration complexity to your applications themselves.

3. API Gateway Pattern

Deploy an API Gateway (e.g., Azure API Management, Kong, Traefik acting as a gateway) in K8S


