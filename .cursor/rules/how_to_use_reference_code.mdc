---
description: 
globs: 
alwaysApply: false
---
How to Fix a Broken Workflow Using a Working Reference Implementation
1. Understand the Problem and the Reference
Clearly define the broken workflow: What is the user action, what is the expected result, and what is actually happening?
Identify the working reference: Find the function, workflow, or script that performs a very similar process and works as intended.
2. Strategies for Fixing the Problem
A. Superficial/Quick Strategy: Function-Level Copying
Copy the function body or logic from the working reference to the broken script.
Change only the necessary variable names or IDs.
Limitation: This often fails if the problem is in the way functions are called, how data flows, or in the callback/event structure.
B. Structural Parity Strategy: Match the Data Flow and Event Graph
List all Inputs, Outputs, and States for the relevant functions/callbacks in both the broken and working scripts.
Draw or write out the event/callback dependency graph for both workflows.
Ensure that:
The same user action updates the same stores/variables in both scripts.
The same callbacks/functions are triggered by the same events.
No extra or missing Inputs/Outputs exist in the broken script compared to the reference.
Tip: Pay special attention to:
Stores or variables that are updated by multiple callbacks.
Callbacks that have multiple Inputs/Outputs.
Chained updates (where one callback’s Output is another’s Input).
Change the broken script so that its callback signatures, Inputs/Outputs, and event flow match the reference exactly.
C. Deep Parity Strategy: Full Contextual Copy
Copy not just the function logic, but also:
The callback signatures (Inputs, Outputs, States, and their order).
The component IDs in the layout.
The order and grouping of updates to stores/variables.
Temporarily comment out or remove any extra logic or callbacks in the broken script.
Test the minimal, reference-matching version.
Incrementally reintroduce any necessary custom logic, testing after each change.
3. Subtleties and Tips
Dash/Callback Systems:
Multiple Inputs being updated in a single user action can cause multiple callback executions.
Dash will trigger a callback once for each Input that changes, unless they are updated in a single atomic operation and Dash batches them.
If you see multiple triggers, check for multiple Inputs being updated or for chained callbacks.
Print Statements:
Use print statements in both the broken and reference scripts to compare the number and order of callback executions.
Atomicity:
Ensure that all necessary state updates happen in a single callback, not spread across chained or overlapping callbacks.
Remove Redundancy:
Only update a store or variable in one place per user action, unless absolutely necessary.
Test Incrementally:
After each change, test the workflow to see if the behavior matches the reference.
4. Checklist for Debugging with a Reference
[ ] Is the user action triggering the same callbacks/functions in both scripts?
[ ] Are the Inputs/Outputs/States for each callback/function identical?
[ ] Are any stores/variables being updated by more than one callback?
[ ] Are there any extra or missing callbacks in the broken script?
[ ] Does the printout or log output match between the two scripts for the same user action?
[ ] After making changes, does the broken workflow now behave exactly like the reference?
5. Example: Dash Callback Parity
Reference (working):
Apply to predict_call...
.
Broken (to fix):
Apply to predict_call...
.
Fix:
Remove extra-store as an Input if it is not needed, or ensure it is only updated by user actions that should trigger the graph update.
6. Final Advice
> True parity is not just about copying code, but about matching the entire data and event flow.
> Always check the callback signatures, event dependencies, and the order of state updates—not just the function logic.
