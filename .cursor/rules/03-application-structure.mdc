---
description:
globs:
alwaysApply: false
---
# Application Architecture

## Overview

The Chromatogram Viewer follows a standard Dash application architecture with a clear separation of UI definition and callback logic.

## Component Interaction

1. [app.py](mdc:app.py) defines the UI layout and initializes the Dash application
2. [callbacks.py](mdc:callbacks.py) contains the interactive logic that connects UI elements
3. [utils.py](mdc:utils.py) and [graph_utils.py](mdc:graph_utils.py) provide supporting functions
4. [EmpowerAPI.py](mdc:EmpowerAPI.py) handles external API communication

## Data Flow

1. User uploads chromatogram data files through the UI
2. Callback functions process the uploaded data
3. The application stores temporary data in the `/temp` directory
4. The processed data is visualized in graphs and tables
5. When needed, the application communicates with external services via the API client

## External Integration

The application can:
1. Load data from local files
2. Fetch data from server storage
3. Communicate with the Empower API service for advanced chromatography analysis

## Deployment Architecture

The application is containerized with Docker and can be deployed to:
1. Local Docker environments
2. Kubernetes clusters
3. Azure cloud services via pipelines

