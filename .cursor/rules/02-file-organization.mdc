---
description: 
globs: 
alwaysApply: false
---
# File Organization

## Core Application Files

- [app.py](mdc:app.py) - Main Dash application setup and UI layout definition
- [callbacks.py](mdc:callbacks.py) - Event handlers and interactive functionality
- [utils.py](mdc:utils.py) - General utility functions for data processing
- [graph_utils.py](mdc:graph_utils.py) - Specialized functions for graph visualization

## API Integration

- [EmpowerAPI.py](mdc:EmpowerAPI.py) - Client for the Empower chromatography data service API

## Configuration Files

- [config.json.template](mdc:config.json.template) - Template for application configuration
- [requirements.txt](mdc:requirements.txt) - Python package dependencies

## Deployment Configuration

- [Dockerfile](mdc:Dockerfile) - Docker container configuration
- [docker-compose.yml](mdc:docker-compose.yml) - Docker Compose deployment setup
- [azure-pipelines.yml](mdc:azure-pipelines.yml) - CI/CD pipeline definition for Azure DevOps

## Documentation

- [README.md](mdc:README.md) - Project documentation and instructions
