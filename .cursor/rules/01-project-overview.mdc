---
description: 
globs: 
alwaysApply: false
---
# Project Overview

This is a Chromatogram Viewer application built with Dash (Python) for analyzing, visualizing, and processing chromatography data.

## Main Components

- [app.py](mdc:app.py) - Main application entry point, defines the Dash app and UI layout
- [callbacks.py](mdc:callbacks.py) - Contains all the callback functions for interactive features
- [utils.py](mdc:utils.py) - Utility functions for data processing and analysis
- [graph_utils.py](mdc:graph_utils.py) - Specialized utilities for graph/plot handling
- [EmpowerAPI.py](mdc:EmpowerAPI.py) - Client for interacting with the Empower API service

## Project Structure

- `/templates` - Contains HTML templates
- `/assets` - Static assets like CSS, JavaScript, and images
- `/temp` - Temporary file storage

## Core Functionality

The application allows users to:
- Upload and visualize chromatogram data
- Manage and display peak tables
- Configure integration parameters and events
- Process samples with background subtraction
- Interact with external services via API clients
