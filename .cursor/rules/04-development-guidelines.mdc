---
description: 
globs: 
alwaysApply: false
---
# Development Guidelines

## Code Style

- Python code follows PEP 8 style guidelines
- Functions include type hints
- Complex operations should include clear, concise comments
- Follow modular programming practices

## Error Handling

- Use explicit try-except blocks for error handling
- Print informative error messages and continue execution when possible
- Log errors for debugging

## Project Structure

- New features should be implemented as separate callback functions in [callbacks.py](mdc:callbacks.py)
- Utility functions should be placed in [utils.py](mdc:utils.py)
- Graph-specific functionality belongs in [graph_utils.py](mdc:graph_utils.py)
- API clients should follow the pattern in [EmpowerAPI.py](mdc:EmpowerAPI.py)

## Development Workflow

- Start with simple, testable implementations
- Incrementally add complexity
- Separate code into distinct blocks by functionality
- When new dependencies are added, update [requirements.txt](mdc:requirements.txt)

## Deployment

- The application is containerized with Docker
- Use the provided [Dockerfile](mdc:Dockerfile) and [docker-compose.yml](mdc:docker-compose.yml)
- CI/CD is handled through Azure Pipelines as defined in [azure-pipelines.yml](mdc:azure-pipelines.yml)
