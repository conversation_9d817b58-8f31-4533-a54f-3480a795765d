import pandas as pd
import base64
import io
import requests
import json
from typing import Dict, Any, Optional, List, Union, Tuple
from netCDF4 import Dataset
import numpy as np
import json, os
from datetime import datetime
import time
import tempfile
import platform
from EmpowerAPI import EmpowerAPIClient
from urllib.parse import urlparse
import uuid
import psycopg2
import psycopg2.extras
import logging
from db_utils import get_db_connection, save_task, update_task_with_response, update_peaktable_feedback, save_peakseg_task, update_peakseg_task_with_response, update_peakseg_feedback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('utils')

# Import database utils with fallback
try:
    from db_utils import get_db_connection
    DB_ENABLED = True
    logger.info("Database module loaded successfully")
except ImportError:
    DB_ENABLED = False
    logger.warning("Database module import failed - database features disabled")
except Exception as e:
    DB_ENABLED = False
    logger.error(f"Error initializing database module: {e}")

# Get SMB credentials from environment variables with fallbacks
SMB_USERNAME = os.environ.get('SMB_USERNAME', 'flow_chem')
SMB_PASSWORD = os.environ.get('SMB_PASSWORD', '39onj!RRpk!@IW^')
SMB_DOMAIN = os.environ.get('SMB_DOMAIN', 'pharmatechs.com')
SMB_SERVER_IP = os.environ.get('SMB_SERVER_IP', '************')

# Optional imports - will be imported when needed
# from smb.SMBConnection import SMBConnection

event_type_options = {
    "Detect Shoulders": "detect_shoulder",
    "Valley to Valley": "valley_to_valley",
    "Set Peak Width (sec)": "peak_width",
    "Set Detection Threshold": "detection_threshold",
    "Set Touchdown %": "touchdown",
    "Set Liftoff %": "liftoff",
    "Set Minimum Area": "min_area", 
    "Set Minimum Height": "min_height",
    "Set Maximum Height": "max_height",
    "Set Maximum Width (sec)": "max_width"
}

def save_task_to_db(payload: Dict[str, Any], request_submitter: str = "system") -> Optional[str]:
    """
    Save the task request to the database.
    
    Parameters:
    -----------
    payload : Dict[str, Any]
        The request payload to save
    request_submitter : str
        Identifier of the user/system submitting the request
    
    Returns:
    --------
    Optional[str]
        The task_id if successful, None if an error occurred
    """
    # Skip if database is not enabled
    if not DB_ENABLED:
        logger.warning("Database is disabled - task not saved to database")
        return None
    
    try:
        # Create a new UUID for the task
        task_id = str(uuid.uuid4())
        
        # Get database connection
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database - task not saved")
            return None
        
        # Create cursor
        cursor = conn.cursor()
        
        # Insert the task
        insert_query = """
        INSERT INTO peaktable_tasks (
            task_id, 
            request_submitter, 
            submit_time, 
            payload, 
            response_status
        ) VALUES (%s, %s, %s, %s, %s)
        """
        
        cursor.execute(
            insert_query, 
            (
                task_id, 
                request_submitter, 
                datetime.now(), 
                psycopg2.extras.Json(payload), 
                "SUBMITTED"
            )
        )
        
        # Commit the transaction
        conn.commit()
        
        # Close cursor and connection
        cursor.close()
        conn.close()
        
        logger.info(f"Task saved to database with ID: {task_id}")
        return task_id
        
    except Exception as e:
        logger.error(f"Error saving task to database: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals() and conn:
            try:
                conn.rollback()
                conn.close()
            except:
                pass
        return None

def update_task_with_response(task_id: str, response_status: str, result: Any = None) -> Optional[str]:
    """
    Update a task in the database with the API response.
    
    Parameters:
    -----------
    task_id : str
        The task ID to update
    response_status : str
        The response status code
    result : Any
        The result from the API
    
    Returns:
    --------
    Optional[str]
        The peaktable_id if created, None if an error occurred
    """
    # Generate a peaktable_id regardless of DB status
    peaktable_id = None
    if response_status in ("200", "201", "success") and result is not None:
        peaktable_id = str(uuid.uuid4())
    
    # Skip database update if disabled
    if not DB_ENABLED:
        logger.warning("Database is disabled - task update skipped")
        return peaktable_id
    
    # Skip if no task_id provided
    if not task_id:
        logger.warning("No task_id provided - skipping database update")
        return peaktable_id
    
    try:
        # Get database connection
        conn = get_db_connection()
        if not conn:
            logger.error("Could not connect to database - task update skipped")
            return peaktable_id
        
        # Create cursor
        cursor = conn.cursor()
        
        # Check if task exists first
        cursor.execute("SELECT task_id FROM peaktable_tasks WHERE task_id = %s", (task_id,))
        if cursor.fetchone() is None:
            logger.error(f"Task {task_id} not found in database")
            cursor.close()
            conn.close()
            return peaktable_id
        
        # Update the task
        update_query = """
        UPDATE peaktable_tasks SET 
            response_status = %s, 
            result_peaktable = %s,
            peaktable_id = %s,
            completion_time = %s
        WHERE task_id = %s
        """
        
        cursor.execute(
            update_query, 
            (
                response_status, 
                psycopg2.extras.Json(result) if result else None,
                peaktable_id,
                datetime.now(),
                task_id
            )
        )
        
        # Commit the transaction
        conn.commit()
        
        # Close cursor and connection
        cursor.close()
        conn.close()
        
        logger.info(f"Task {task_id} updated with status {response_status}" + 
                   (f" and peaktable_id {peaktable_id}" if peaktable_id else ""))
        return peaktable_id
        
    except Exception as e:
        logger.error(f"Error updating task in database: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals() and conn:
            try:
                conn.rollback()
                conn.close()
            except:
                pass
        return peaktable_id

def read_CH2(source, from_string=False):
    """
    Read and parse a .CH2 file or content string, extracting metadata and numerical data.

    Args:
        source: Either a file path (str) or content string, depending on from_string parameter
        from_string: If True, source is treated as content string; if False, as file path

    Returns:
        tuple: (metadata_df, data_df) where
               metadata_df contains the header information
               data_df contains the time series data with columns 'Time (min)' and sample name
               OR
        DataFrame: A DataFrame with 'Time (min)' and sample name columns (legacy format)
        OR
        None: If an error occurs
    """
    try:
        if from_string:
            lines = source.split('\n')
        else:
            with open(source, 'r') as f:
                lines = f.readlines()

        # Parse headers and metadata
        headers = lines[0].strip().split('\t')
        values_line = lines[1].strip()
        if not values_line:
            print(f"Error: Could not read metadata values")
            return None
        metadata_values = values_line.split('\t')
        if not metadata_values:
            print(f"Error: Metadata line format incorrect")
            return None

        # Create metadata DataFrame
        metadata_df = pd.DataFrame([metadata_values], columns=headers)
        sample_name = metadata_values[0]  # Extract the SampleName

        # Parse time series data (skip header, metadata, and blank line)
        data_lines = [line.strip() for line in lines[3:] if line.strip()]
        data = [list(map(float, line.split())) for line in data_lines]

        # Create data DataFrame with proper column names
        data_df = pd.DataFrame(data, columns=['Time (min)', sample_name])

        if from_string:
            return metadata_df, data_df
        else:
            return data_df  # Legacy format for backward compatibility
    except FileNotFoundError:
        print(f"Error: File not found at {source}")
        return None
    except IndexError:
        print(f"Error: Problem parsing metadata line. Check format.")
        return None
    except Exception as e:
        print(f"An unexpected error occurred while processing: {e}")
        return None

def parse_smb_url(smb_url: str) -> tuple:
    """
    Parse smb://host/share/folder into (host, share, folder_path).
    """
    p = urlparse(smb_url)
    host = p.hostname
    # strip leading slash, then split once on '/'
    parts = p.path.lstrip("/").split("/", 1)
    share = parts[0]
    folder = parts[1] if len(parts) > 1 else ""
    return host, share, folder

def find_recent_files_with_suffix(smb_url: str, suffix: str, extension: str, time_interval: int = 600, exact_match: bool = True, search_pattern: str = None) -> List[str]:
    """
    Find files in the specified SMB directory that match the given criteria.

    Args:
        smb_url (str): The SMB URL (e.g., 'smb://server/share/directory').
        suffix (str): The suffix that the filename should contain or end with (before the extension).
        extension (str): The file extension to filter by (include the dot, e.g., '.CH2').
        time_interval (int): The time interval in seconds within which the file should have been modified.
        exact_match (bool, optional): If True, look for exact suffix at the end. If False, look for suffix anywhere in filename.
        search_pattern (str, optional): If provided, use this pattern for SMB file search. Default is None.

    Returns:
        List[str]: A list of filenames that meet the criteria.
    """
    recent_files = []
    current_time = time.time()

    try:
        import socket
        from smb.SMBConnection import SMBConnection

        # Normalize extension format
        if not extension.startswith('.'):
            extension = f".{extension}"

        # Convert suffix to string if it's not already
        suffix = str(suffix)

        # Parse the SMB URL
        server_name, share_name, dir_path = parse_smb_url(smb_url)

        # Use local machine name
        client_name = socket.gethostname()

        print(f"Attempting SMB connection to server: {server_name}")

        conn = SMBConnection(
            SMB_USERNAME,
            SMB_PASSWORD,
            client_name,
            server_name,
            domain=SMB_DOMAIN,
            use_ntlm_v2=True,
            is_direct_tcp=True
        )

        print("SMB connection object created, attempting to connect...")
        if not conn.connect(SMB_SERVER_IP, 445, timeout=15):
            print(f"Failed to connect to SMB server: Connection returned false")
            return recent_files

        print("SMB connection successful")

        try:
            print(f"Attempting to list path - Share: {share_name}, Directory: {dir_path}")
            # Use the original search_pattern if provided, or fallback to listing all files
            try:
                if search_pattern:
                    print(f"Using search pattern: {search_pattern}")
                    file_list = conn.listPath(share_name, dir_path, pattern=search_pattern)
                else:
                    file_list = conn.listPath(share_name, dir_path)
                print(f"Successfully listed directory, found {len(file_list)} items")
            except Exception as e:
                print(f"Error using pattern search: {e}, falling back to regular listing")
                file_list = conn.listPath(share_name, dir_path)

            print("Processing file list...")
            for file_info in file_list:
                if not file_info.isDirectory:
                    filename = file_info.filename

                    # Check if file was modified within the time interval
                    if (current_time - file_info.last_write_time) < time_interval:

                        # Check if filename has the correct extension
                        if filename.endswith(extension):
                            name_without_ext = filename[:-len(extension)]

                            # Check if filename matches our criteria based on exact_match parameter
                            if exact_match and name_without_ext.endswith(suffix):
                                recent_files.append(filename)
                            elif not exact_match and suffix in name_without_ext:
                                recent_files.append(filename)

            # Sort files by modification time (newest first)
            if recent_files:
                recent_files.sort(
                    key=lambda f: next(
                        (fi.last_write_time for fi in file_list if fi.filename == f),
                        0
                    ),
                    reverse=True
                )

        finally:
            conn.close()

    except Exception as e:
        print(f"Error listing SMB directory {smb_url}: {e}")

    return recent_files

def read_smb_file(smb_url: str) -> bytes:
    """
    Read a file from an SMB share using the reliable connection method.

    Args:
        smb_url (str): Full SMB URL to the file (e.g., 'smb://server/share/path/to/file')

    Returns:
        bytes: The file content as bytes, or None if an error occurred.
    """
    try:
        import socket
        from smb.SMBConnection import SMBConnection
        from io import BytesIO

        # Parse the SMB URL
        server_name, share_name, file_path = parse_smb_url(smb_url)

        # Use local machine name
        client_name = socket.gethostname()

        print(f"Attempting to create SMB connection to {server_name}...")

        conn = SMBConnection(
            SMB_USERNAME,
            SMB_PASSWORD,
            client_name,
            server_name,
            domain=SMB_DOMAIN,
            use_ntlm_v2=True,
            is_direct_tcp=True
        )

        print("Attempting to connect to server...")
        if not conn.connect(SMB_SERVER_IP, 445, timeout=15):
            print(f"Failed to connect to SMB server: {server_name}")
            return None

        try:
            # Read the file
            file_obj = BytesIO()
            conn.retrieveFile(share_name, file_path, file_obj)
            return file_obj.getvalue()
        finally:
            conn.close()

    except Exception as e:
        print(f"Error reading SMB file {smb_url}: {e}")
        return None

def read_project_channel(project_name: str, channel_id: int, account: str = "frank",
                          password: str = "123456", database_name: str = "WAT18"):
    """
    Read a project channel from Empower and return the chromatogram data.

    Args:
        project_name (str): The Empower project name (e.g., "202407\\Clinical_Project\\C18053047").
        channel_id (int): The channel ID to export.
        account (str, optional): The Empower account username.
        password (str, optional): The Empower account password.
        database_name (str, optional): The Empower database name.

    Returns:
        pandas.DataFrame: The chromatogram data, or None if an error occurred.
    """
    try:
        # Make sure Empower callback context is set
        import dash
        if hasattr(dash, 'callback_context'):
            dash.callback_context._chrome_app_project_name = project_name
            
        # Initialize Empower API client
        client = EmpowerAPIClient()

        # Login to the project
        print(f"Logging in to project: {project_name}")
        login_result = client.login_project(
            project_name=project_name,
            account=account,
            password=password,
            database_name=database_name
        )

        if not login_result:
            print("Error: Failed to login to Empower project")
            return None

        print("Login successful. Exporting chromatogram channel...")

        # Export the chromatogram channel
        export_result = client.export_chrom_channel_2d(
            export_name="export",
            channel_ids=[channel_id],
            xml_filename="chrom_test.xml"
        )

        if not export_result:
            print("Error: Failed to export chromatogram channel")
            return None

        print("Export successful. Accessing via SMB...")

        # Wait a moment for the file to be exported
        time.sleep(5)  # Reduced from 10 to 5 seconds as we're using a shorter time window

        # Search for the file in SMB share with retry logic and specific pattern
        smb_url = "smb://w09empdr01/CZEMPDR/Export Test"
        search_pattern = f"*{channel_id}.CH2"  # More specific pattern
        
        # Add retry logic for finding the file
        max_retries = 3
        retry_delay = 5  # seconds
        files = None

        for attempt in range(max_retries):
            print(f"SMB file search attempt {attempt+1}/{max_retries} with pattern {search_pattern}...")
            files = find_recent_files_with_suffix(
                smb_url, 
                str(channel_id), 
                '.CH2',
                time_interval=86400 * 30,  # 30 days instead of 10 minutes
                search_pattern=search_pattern
            )
            print(f"Attempt {attempt+1}: Found files via SMB: {files}")

            if files:
                break

            if attempt < max_retries - 1:
                print(f"No files found, retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)

        if not files:
            print("Error: No matching files found via SMB after multiple attempts")
            return None

        # Use the most recent file
        target_file = files[0]  # files are already sorted by modification time
        full_smb_url = f"{smb_url}/{target_file}"
        print(f"Using file: {full_smb_url}")

        # Read the file via SMB
        file_content = read_smb_file(full_smb_url)
        if not file_content:
            print("Error: Failed to read file via SMB")
            return None

        # Save to temporary file and read
        with tempfile.NamedTemporaryFile(suffix='.CH2', delete=False) as temp_file:
            temp_file.write(file_content)
            temp_path = temp_file.name

        try:
            # Read the CH2 file using the read_CH2 function
            content = file_content.decode('utf-8', errors='replace')
            metadata_df, data_df = read_CH2(content, from_string=True)

            if data_df is not None:
                # Get the original filename without extension to use as the legend
                ch2_name_without_ext = os.path.splitext(target_file)[0]
                
                # Create a proper legend with Channel ID and (Sample) tag
                legend_name = f"Channel {channel_id} (Sample)"
                
                # Rename the columns for consistent handling
                # First column is time, second is signal
                if len(data_df.columns) >= 2:
                    data_df.columns = ["Time (min)", legend_name]
                    print(f"Set legend to: {legend_name}")
                else:
                    print("Warning: unexpected column structure in CH2 data")
                    # Try to handle unexpected format
                    if 'Value' in data_df.columns:
                        data_df.rename(columns={'Value': legend_name}, inplace=True)

                print("Successfully read file via SMB")
                return data_df
        finally:
            # Clean up temp file
            try:
                os.unlink(temp_path)
            except Exception:
                pass

        return None

    except Exception as e:
        print(f"Error in read_project_channel: {e}")
        import traceback
        traceback.print_exc()
        return None

def read_cdf(file_path):
    """
    Reads a .cdf (NetCDF) file containing chromatographic data,
    extracts time and signal, and returns a pandas DataFrame
    formatted with columns 'Time (min)' and the sample name.

    """
    try:
        # Open the .cdf file for reading
        with Dataset(file_path, 'r') as cdf_file:

            # --- Extract Metadata ---
            # Get Sample Name from global attributes
            if hasattr(cdf_file, 'sample_name') and cdf_file.sample_name:
                sample_name = cdf_file.sample_name
            else:
                print(f"Warning: Global attribute 'sample_name' not found or empty in {file_path}. Using filename as fallback.")
                # Fallback: use filename without extension
                sample_name = os.path.splitext(os.path.basename(file_path))[0]

            # Get Time Unit from global attributes
            time_unit = 'seconds' # Default assumption
            if hasattr(cdf_file, 'retention_unit') and cdf_file.retention_unit:
                unit_attr = cdf_file.retention_unit.lower()
                if 'minute' in unit_attr: # Check if 'minute' or 'minutes' is present
                     time_unit = 'minutes'
                elif 'second' in unit_attr: # Check if 'second' or 'seconds' is present
                     time_unit = 'seconds'
                else:
                    print(f"Warning: Unrecognized 'retention_unit' ('{cdf_file.retention_unit}') in {file_path}. Assuming seconds.")
            else:
                 print(f"Warning: Global attribute 'retention_unit' not found or empty in {file_path}. Assuming seconds.")


            # --- Extract Data Arrays ---
            # Define expected variable names (common variations)
            time_var_names = ['raw_data_retention', 'actual_retention', 'retention_time', 'time_values']
            signal_var_names = ['ordinate_values', 'intensity_values', 'detector_signal']

            # Find the correct variable names
            time_var = next((name for name in time_var_names if name in cdf_file.variables), None)
            signal_var = next((name for name in signal_var_names if name in cdf_file.variables), None)

            if not time_var:
                print(f"Error: Could not find a suitable time variable (tried: {time_var_names}) in {file_path}.")
                return None
            if not signal_var:
                print(f"Error: Could not find a suitable signal variable (tried: {signal_var_names}) in {file_path}.")
                return None

            print(f"Info: Using time variable '{time_var}' and signal variable '{signal_var}' from {file_path}.")

            # Extract time and signal data (as numpy arrays)
            time_data = cdf_file.variables[time_var][:]
            signal_data = cdf_file.variables[signal_var][:]

            # Ensure data is numpy array (usually is, but handles potential scalar return)
            if not isinstance(time_data, np.ndarray):
                 time_data = np.array([time_data])
            if not isinstance(signal_data, np.ndarray):
                 signal_data = np.array([signal_data])

            # Check if arrays are compatible
            if time_data.shape != signal_data.shape:
                 print(f"Error: Time ({time_data.shape}, var:'{time_var}') and signal ({signal_data.shape}, var:'{signal_var}') arrays have incompatible shapes in {file_path}.")
                 return None
            if time_data.ndim != 1:
                 print(f"Error: Time and signal data must be 1-dimensional arrays in {file_path}.")
                 return None


            # --- Convert Time to Minutes if Necessary ---
            if time_unit == 'seconds':
                time_data_minutes = time_data / 60.0
                print(f"Info: Converted time data from seconds to minutes for {file_path}.")
            else: # Assume minutes otherwise
                time_data_minutes = time_data
                print(f"Info: Assuming time data is already in minutes for {file_path}.")

            # --- Create DataFrame ---
            df = pd.DataFrame({
                'Time (min)': time_data_minutes,
                sample_name: signal_data  # Use sample_name as the column header
            })

            return df

    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return None
    except Exception as e:
        # Catch other potential errors during file reading/processing
        # (e.g., issues with netCDF4 library, corrupted file)
        print(f"An unexpected error occurred while processing {file_path}: {e}")
        return None

# Parse Excel file
def parse_excel(contents):
    """
    Parse uploaded Excel file and convert to pandas DataFrame

    Parameters:
    -----------
    contents : str
        Base64 encoded content of the uploaded file

    Returns:
    --------
    df : pandas DataFrame or None
        DataFrame containing the Excel file contents, or None if parsing failed
    """
    if contents is None:
        return None

    # Split the content string and ignore the content_type part
    _, content_string = contents.split(',')
    decoded = base64.b64decode(content_string)

    try:
        df = pd.read_excel(io.BytesIO(decoded))
        return df
    except Exception as e:
        print(f"Error parsing Excel file: {e}")
        return None

# Parse CSV file
def parse_csv(contents):
    """
    Parse uploaded CSV file and convert to pandas DataFrame

    Parameters:
    -----------
    contents : str
        Base64 encoded content of the uploaded file

    Returns:
    --------
    df : pandas DataFrame or None
        DataFrame containing the CSV file contents, or None if parsing failed
    """
    if contents is None:
        return None

    # Split the content string and ignore the content_type part
    _, content_string = contents.split(',')
    decoded = base64.b64decode(content_string)

    try:
        df = pd.read_csv(io.BytesIO(decoded))
        return df
    except Exception as e:
        print(f"Error parsing CSV file: {e}")
        return None

# Format exponential values
def format_exponential(value):
    """
    Format a number in scientific notation

    Parameters:
    -----------
    value : float
        Number to format

    Returns:
    --------
    str
        Formatted string in scientific notation (e.g., '6.00e+01')
    """
    num = float(value)
    if num == 0:
        return '0.00e+00'
    exp = int('{:e}'.format(num).split('e')[1])
    mantissa = num / (10 ** exp)
    return f"{mantissa:.2f}e+{exp:02d}"

def process_parameter_excel(contents: str) -> Dict[str, Any]:
    """
    Process uploaded Integration Parameters Excel file into API format

    Parameters:
    -----------
    contents : str
        Base64 encoded content of the uploaded file

    Returns:
    --------
    Dict[str, Any]
        Formatted integration parameters
    """
    df = parse_excel(contents)
    if df is None:
        return {}

    # Expected Excel format:
    # Parameter | Value
    # peak_width | 34.0
    params = {
        "peak_width": 0,
        "detection_threshold": 0,
        "liftoff": 0,
        "touchdown": 0,
        "min_area": 0,
        "min_height": 0,
        "integration_start": 0,
        "integration_end": 0
    }

    try:
        for _, row in df.iterrows():
            param_name = row['Parameter'].lower().strip()
            if param_name in params:
                params[param_name] = float(row['Value'])
    except Exception as e:
        print(f"Error processing parameters Excel: {e}")

    return params

def process_events_excel(contents: str) -> list:
    """
    Process uploaded Integration Events Excel file into API format

    Parameters:
    -----------
    contents : str
        Base64 encoded content of the uploaded file

    Returns:
    --------
    list
        List of formatted integration events
    """
    df = parse_excel(contents)
    if df is None:
        return []

    # Expected Excel format:
    # Time | Type | Value | Stop
    valid_types = ["peak_width", "detection_threshold", "liftoff",
                  "touchdown", "detect_shoulder", "valley_to_valley"]
    events = []

    try:
        for _, row in df.iterrows():
            event_type = row['Type'].lower().strip()
            if event_type in valid_types:
                event = {
                    "param_type": event_type,
                    "param_value": float(row['Value']),
                    "param_start": float(row['Time']),
                    "param_end": float(row.get('Stop', row['Time']))
                }
                events.append(event)
    except Exception as e:
        print(f"Error processing events Excel: {e}")

    return events

def process_peaktable_api(
    chromatogram_data: Dict[str, Any],
    integration_params: Dict[str, Any],
    integration_events: list,
    request_submitter: str = None,  # Make this optional for backward compatibility
    api_url: str = "http://*************:31392/peaktable"
    # api_url: str = "http://algorithm:5000/peaktable"
) -> Tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """
    Send request to chromatogram processing API with validation and correction of parameters.
    
    Parameters:
    -----------
    chromatogram_data : Dict[str, Any]
        Dictionary containing:
        - time_steps: list of float
        - signal: list of float
    integration_params : Dict[str, Any]
        Integration parameters
    integration_events : list
        List of integration events
    request_submitter : str, optional
        Identifier of the user/system submitting the request (not used in API call)
    api_url : str
        URL for the PeakTable API
        
    Returns:
    --------
    Tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]
        A tuple containing (api_result, api_payload)
        api_result: The result from the API call, or None if failed
        api_payload: The payload that was sent to the API, or None if failed
    """
    try:
        # 1. Validate integration_start and integration_end
        int_start = float(integration_params.get("integration_start", 0))
        int_end = float(integration_params.get("integration_end", 0))
        
        # Ensure integration_end is not less than integration_start
        if int_end < int_start:
            logger.warning(f"Integration end ({int_end}) is less than integration start ({int_start}). Setting end to start.")
            int_end = int_start
            integration_params["integration_end"] = int_start

        # 2. Validate and filter integration events
        valid_events = []
        for event in integration_events:
            event_time = float(event.get("Time", 0))
            
            # Remove events outside integration range
            if event_time < int_start:
                logger.warning(f"Removing event at time {event_time} as it's less than integration start ({int_start})")
                continue
                
            if event_time >= int_end:
                logger.warning(f"Removing event at time {event_time} as it's greater than or equal to integration end ({int_end})")
                continue
                
            valid_events.append(event)
        
        events_filtered = len(integration_events) != len(valid_events)
        if events_filtered:
            logger.info(f"Filtered {len(integration_events) - len(valid_events)} events that were outside integration range")
        
        # Transform valid integration events
        transformed_events = []
        events_by_type = {}
        for event in valid_events:
            event_type = event.get("Type", "")
            event_type_mapped = event_type_options.get(event_type, "unknown")
            
            if event_type_mapped == "unknown":
                logger.warning(f"Unknown event type: '{event_type}'. Check event_type_options dictionary.")
            else:
                logger.debug(f"Event type mapping: '{event_type}' -> '{event_type_mapped}'")
            
            param_end = None
            if event.get("Stop") is not None:
                stop_time = float(event.get("Stop"))
                if stop_time <= int_end:
                    param_end = stop_time
                else:
                    logger.warning(f"Event stop time {stop_time} exceeds integration end. Adjusting.")
                    param_end = None
            
            event_entry = {
                "param_type": event_type_mapped,
                "param_value": float(event.get("Value", 0)) if event.get("Value") is not None else 0,
                "param_start": float(event.get("Time", 0)),
                "param_end": param_end
            }
            transformed_events.append(event_entry)
            events_by_type.setdefault(event_type_mapped, []).append(event_entry)

        # Update param_end for events
        for param_type, events in events_by_type.items():
            events.sort(key=lambda x: x["param_start"])
            for i in range(len(events)):
                if events[i]["param_end"] is not None:
                    continue
                    
                if i < len(events) - 1:
                    events[i]["param_end"] = events[i + 1]["param_start"]
                else:
                    events[i]["param_end"] = int_end

        # Reconstruct and sort transformed_events
        transformed_events = []
        for events in events_by_type.values():
            transformed_events.extend(events)
        transformed_events.sort(key=lambda x: x["param_start"])

        # Format API payload
        payload = {
            "signal": chromatogram_data.get("signal", []),
            "time_steps": chromatogram_data.get("time_steps", []),
            "integration_start": int_start,
            "integration_end": int_end,
            "integration_parameter": {
                "peak_width": integration_params.get("peak_width", 0),
                "detection_threshold": integration_params.get("detection_threshold", 0),
                "liftoff": integration_params.get("liftoff", 0),
                "touchdown": integration_params.get("touchdown", 0),
                "min_area": integration_params.get("min_area", 0),
                "min_height": integration_params.get("min_height", 0),
                "integration_events": transformed_events
            }
        }
        
        # Include the columns in the payload for source tracking
        if "columns" in chromatogram_data:
            payload["columns"] = chromatogram_data["columns"]

        logger.info(f"Integration start/end: {int_start}/{int_end}")
        logger.debug(f"Integration events: {len(transformed_events)} events")

        # Save request to temp file
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        out_dir = "temp"
        os.makedirs(out_dir, exist_ok=True)
        path = os.path.join(out_dir, f"{ts}.json")

        with open(path, "w", encoding="utf-8") as f:
            json.dump(payload, f, indent=2, ensure_ascii=False)

        logger.info(f"Request parameters saved to {path}")
        
        # Make API request
        headers = {'Content-Type': 'application/json'}
        logger.info(f"Sending request to API: {api_url}")
        response = requests.post(api_url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()

        result = response.json()
        logger.info(f"API response received: {len(str(result))} bytes")
        
        # Handle response - expect just a list of peaks (like peaktable API)
        if isinstance(result, list):
            peaks = result
            logger.info(f"Successfully received {len(peaks)} peaks from API")
            return peaks, payload
        elif isinstance(result, dict) and "peaks" in result:
            # Fallback for old format if needed
            peaks = result["peaks"]
            logger.info(f"Successfully received {len(peaks)} peaks from API (legacy format)")
            return peaks, payload
        else:
            logger.error(f"Unexpected API response format: {type(result)}")
            return None, payload

    except requests.exceptions.RequestException as e:
        logger.error(f"API Request Error: {e}")
        return None, None
    except json.JSONDecodeError as e:
        logger.error(f"JSON Decode Error: {e}")
        return None, None
    except Exception as e:
        logger.error(f"Unexpected Error: {e}")
        return None, None

def save_peaktable_to_db(payload: Dict[str, Any], result: Dict[str, Any], request_submitter: str = "system", sample_metadata: Dict[str, Any] = None) -> Optional[str]:
    """
    Save a peak table request and response to the database.
    This function should be called after the UI is updated.
    
    Parameters:
    -----------
    payload : Dict[str, Any]
        The request payload that was sent to the API
    result : Dict[str, Any]
        The result returned from the API
    request_submitter : str
        Identifier of who/what submitted the request
    sample_metadata : Dict[str, Any], optional
        Metadata about the sample file (filename, project, channel_id)
        
    Returns:
    --------
    Optional[str]: The peaktable_id if successful, None if failed
    """
    # Extract sample source information
    sample_source_type = None
    sample_source_info = None
    
    try:
        # First extract potential sample info from the chromatogram columns
        if payload and "columns" in payload and len(payload["columns"]) >= 2:
            sample_legend = payload["columns"][1]  # Second column is the sample name
            
            # Check if it's a locally uploaded file (second column is the filename)
            if not sample_legend.startswith("Channel"):
                sample_source_type = "local_file"
                sample_source_info = sample_legend  # This is the filename from payload["columns"][1]
                print(f"Identified local file source: {sample_source_info}")
            
            # Check if it's from cloud upload (has Channel in the name)
            elif "Channel" in sample_legend and "(Sample)" in sample_legend:
                # Extract project name from the context if available
                import dash
                if hasattr(dash, 'callback_context') and hasattr(dash.callback_context, '_chrome_app_project_name'):
                    project_name = dash.callback_context._chrome_app_project_name
                    if project_name:
                        sample_source_type = "cloud"
                        # Extract channel ID from the legend (format: "Channel {id} (Sample)")
                        import re
                        match = re.search(r'Channel\s+(\d+)', sample_legend)
                        channel_id = match.group(1) if match else None
                        sample_source_info = f"{project_name}:{channel_id}" if channel_id else project_name
                        print(f"Identified cloud source: {sample_source_info}")
        
        # If we couldn't extract from payload, check sample_metadata
        if not sample_source_type and sample_metadata:
            if "filename" in sample_metadata:
                sample_source_type = "local_file"
                sample_source_info = sample_metadata["filename"]
            elif "project_name" in sample_metadata:
                sample_source_type = "cloud"
                sample_source_info = f"{sample_metadata['project_name']}:{sample_metadata.get('channel_id', '')}"
    
    except Exception as e:
        print(f"Warning: Error extracting sample source info: {e}")
        # Continue execution, sample source is optional
    
    # Save to database
    task_id = None
    peaktable_id = None
    
    try:
        # First save the task to get an ID
        if isinstance(payload, dict):
            task_id = save_task(payload, request_submitter, 
                               sample_source_type=sample_source_type, 
                               sample_source_info=sample_source_info)
            
            if task_id and isinstance(result, dict):
                # Then update with the response
                peaktable_id = update_task_with_response(task_id, "200", result)
                if not peaktable_id:
                    logger.error(f"Failed to update task {task_id} with response")
            elif not task_id:
                logger.error("Failed to save peaktable task")
            
            # Always return the peaktable_id (which might be None if there was an error)
            return peaktable_id
        else:
            logger.error(f"Invalid payload type: {type(payload)}")
            return None
    except Exception as e:
        logger.error(f"Error saving peaktable: {e}")
        return None

def save_peaktable_feedback(peaktable_id, feedback_data, operation_type="manual"):
    """
    Saves peaktable feedback to the database
    
    Args:
        peaktable_id: The ID of the peak table
        feedback_data: JSON containing the peak data
        operation_type: The type of operation being performed
    """
    try:
        # Log what we're about to save
        logger.info(f"Saving feedback for peaktable_id: {peaktable_id}")
        logger.debug(f"Feedback data structure: {type(feedback_data)}")
        
        # Update the peaktable_feedbacks table with the new data
        success = update_peaktable_feedback(peaktable_id, feedback_data, operation_type)
        if success:
            logger.info(f"Successfully saved feedback for peaktable_id: {peaktable_id}")
        else:
            logger.error(f"Failed to save feedback for peaktable_id: {peaktable_id}")
        return success
    except Exception as e:
        logger.error(f"Error saving peak table feedback: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def save_peakseg_to_db(payload: Dict[str, Any], result: Dict[str, Any], request_submitter: str = "system", sample_metadata: Dict[str, Any] = None) -> Optional[str]:
    """
    Save a peakseg request and response to the database.
    This function should be called after the UI is updated.
    
    Parameters:
    -----------
    payload : Dict[str, Any]
        The request payload that was sent to the API
    result : Dict[str, Any]
        The result returned from the API
    request_submitter : str
        Identifier of who/what submitted the request
    sample_metadata : Dict[str, Any], optional
        Metadata about the sample file (filename, project, channel_id)
        
    Returns:
    --------
    Optional[str]: The peakseg_id if successful, None if failed
    """
    # Extract sample source information
    sample_source_type = None
    sample_source_info = None
    
    try:
        # First extract potential sample info from the chromatogram columns
        if payload and "columns" in payload and len(payload["columns"]) >= 2:
            sample_legend = payload["columns"][1]  # Second column is the sample name
            
            # Check if it's a locally uploaded file (second column is the filename)
            if not sample_legend.startswith("Channel"):
                sample_source_type = "local_file"
                sample_source_info = sample_legend  # This is the filename from payload["columns"][1]
                print(f"Identified local file source: {sample_source_info}")
            
            # Check if it's from cloud upload (has Channel in the name)
            elif "Channel" in sample_legend and "(Sample)" in sample_legend:
                # Extract project name from the context if available
                import dash
                if hasattr(dash, 'callback_context') and hasattr(dash.callback_context, '_chrome_app_project_name'):
                    project_name = dash.callback_context._chrome_app_project_name
                    if project_name:
                        sample_source_type = "cloud"
                        # Extract channel ID from the legend (format: "Channel {id} (Sample)")
                        import re
                        match = re.search(r'Channel\s+(\d+)', sample_legend)
                        channel_id = match.group(1) if match else None
                        sample_source_info = f"{project_name}:{channel_id}" if channel_id else project_name
                        print(f"Identified cloud source: {sample_source_info}")
        
        # If we couldn't extract from payload, check sample_metadata
        if not sample_source_type and sample_metadata:
            if "filename" in sample_metadata:
                sample_source_type = "local_file"
                sample_source_info = sample_metadata["filename"]
            elif "project_name" in sample_metadata:
                sample_source_type = "cloud"
                sample_source_info = f"{sample_metadata['project_name']}:{sample_metadata.get('channel_id', '')}"
    
    except Exception as e:
        print(f"Warning: Error extracting sample source info: {e}")
        # Continue execution, sample source is optional
    
    # Save to database
    task_id = None
    peakseg_id = None
    
    try:
        # First save the task to get an ID
        if isinstance(payload, dict):
            task_id = save_peakseg_task(payload, request_submitter, 
                                       sample_source_type=sample_source_type, 
                                       sample_source_info=sample_source_info)
            
            if task_id and isinstance(result, dict):
                # Then update with the response
                peakseg_id = update_peakseg_task_with_response(task_id, "200", result)
                if not peakseg_id:
                    logger.error(f"Failed to update peakseg task {task_id} with response")
            elif not task_id:
                logger.error("Failed to save peakseg task")
            
            # Always return the peakseg_id (which might be None if there was an error)
            return peakseg_id
        else:
            logger.error(f"Invalid payload type: {type(payload)}")
            return None
    except Exception as e:
        logger.error(f"Error saving peakseg: {e}")
        return None

def save_peakseg_feedback(peakseg_id, feedback_data, operation_type="manual"):
    """
    Saves peakseg feedback to the database
    
    Args:
        peakseg_id: The ID of the peakseg result
        feedback_data: JSON containing the peak data
        operation_type: The type of operation being performed
    """
    try:
        # Log what we're about to save
        logger.info(f"Saving feedback for peakseg_id: {peakseg_id}")
        logger.debug(f"Feedback data structure: {type(feedback_data)}")
        
        # Update the peakseg_feedbacks table with the new data
        success = update_peakseg_feedback(peakseg_id, feedback_data, operation_type)
        if success:
            logger.info(f"Successfully saved feedback for peakseg_id: {peakseg_id}")
        else:
            logger.error(f"Failed to save feedback for peakseg_id: {peakseg_id}")
        return success
    except Exception as e:
        logger.error(f"Error saving peakseg feedback: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def process_identify_peaks_api(
    chromatogram_data: Dict[str, Any],
    integration_start: float,
    integration_end: float,
    baselines: List[Dict[str, Any]],
    min_area_percent: float = 0.05,
    api_url: str = "http://*************:31392/peakseg"
    # api_url: str = "http://algorithm:5000/peakseg"
) -> Tuple[Optional[List[Dict[str, Any]]], Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """
    Send request to the identify peaks API.
    
    Parameters:
    -----------
    chromatogram_data : Dict[str, Any]
        Dictionary containing:
        - time_steps: list of float
        - signal: list of float
    integration_start : float
        Start time for integration range
    integration_end : float
        End time for integration range
    baselines : List[Dict[str, Any]]
        List of baseline regions, each containing:
        - start: float (start time)
        - end: float (end time)
    min_area_percent : float, optional
        Minimum area percentage for peak detection (default: 0.05)
    api_url : str
        URL for the Identify Peaks API
        
    Returns:
    --------
    Tuple[Optional[List[Dict[str, Any]]], Optional[Dict[str, Any]], Optional[Dict[str, Any]]]
        A tuple containing (peak_list, api_payload, integration_params)
        peak_list: List of detected peaks, or None if failed
        api_payload: The payload that was sent to the API, or None if failed
        integration_params: Integration parameters from API response, or None if failed
    """
    try:
        # Validate input data
        if not isinstance(chromatogram_data, dict) or 'time_steps' not in chromatogram_data or 'signal' not in chromatogram_data:
            logger.error("Invalid chromatogram data format")
            return None, None, None
            
        time_steps = chromatogram_data.get("time_steps", [])
        signal = chromatogram_data.get("signal", [])
        
        if len(time_steps) != len(signal) or len(time_steps) == 0:
            logger.error("Invalid or empty chromatogram data")
            return None, None, None
            
        # Validate and adjust integration range
        # Ensure integration_start is at least 0.5
        if integration_start < 0.5:
            logger.info(f"Adjusting integration_start from {integration_start} to 0.5 (minimum required)")
            integration_start = 0.5
            
        # Ensure integration_end is at least integration_start
        if integration_end < integration_start:
            logger.info(f"Adjusting integration_end from {integration_end} to {integration_start} (must be >= integration_start)")
            integration_end = integration_start
            
        if integration_end <= integration_start:
            logger.warning(f"Integration end ({integration_end}) must be greater than start ({integration_start})")
            return None, None, None
            
        # Process baselines - convert to valid baseline regions
        processed_baselines = []
        for baseline in baselines:
            try:
                start = baseline.get("start", "")
                end = baseline.get("end", "")
                
                # Only include baselines with both start and end values
                if start != "" and end != "":
                    processed_baselines.append({
                        "start": float(start),
                        "end": float(end)
                    })
            except (ValueError, TypeError):
                continue
                
        # Create API payload
        payload = {
            "signal": signal,
            "time_steps": time_steps,
            "integration_start": integration_start,
            "integration_end": integration_end,
            "baselines": processed_baselines,
            "min_area_percent": min_area_percent
        }
        
        # Include columns if available
        if "columns" in chromatogram_data:
            payload["columns"] = chromatogram_data["columns"]

        # --- DEBUG: Save payload to temp folder ---
        try:
            temp_dir = os.path.join(os.path.dirname(__file__), 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            ts = int(time.time() * 1000)
            debug_path = os.path.join(temp_dir, f'identify_peaks_payload_{ts}.json')
            with open(debug_path, 'w') as f:
                json.dump(payload, f, indent=2)
        except Exception as e:
            logger.warning(f"Could not save debug payload: {e}")
        # --- END DEBUG ---

        logger.info(f"Sending identify peaks request to API: {api_url}")
        logger.info(f"Integration range: {integration_start} - {integration_end}")
        logger.info(f"Baselines: {len(processed_baselines)}")
        
        # Make API request
        headers = {'Content-Type': 'application/json'}
        response = requests.post(api_url, json=payload, headers=headers, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        logger.info(f"API response received: {len(str(result))} bytes")
        
        # Handle response - expect dict with peak_table and integration_parameter
        if isinstance(result, dict) and "peak_table" in result:
            peaks = result["peak_table"]
            # Extract and transform integration parameters for UI display
            integration_params = result.get('integration_parameter', {})
            
            # Transform integration events for better display
            if 'integration_events' in integration_params:
                transformed_events = []
                for event in integration_params['integration_events']:
                    # Create reverse mapping from event_type_options values to keys
                    reverse_event_mapping = {v: k for k, v in event_type_options.items()}
                    
                    # Transform the event
                    transformed_event = {
                        "Time (min)": round(float(event.get("param_start", 0)), 3),
                        "Type": reverse_event_mapping.get(event.get("param_type", ""), event.get("param_type", "")),
                        "Value": event.get("param_value", None),
                        "Stop (min)": round(float(event.get("param_end", 0)), 3)
                    }
                    transformed_events.append(transformed_event)
                
                # Replace the original events with transformed ones
                integration_params['integration_events'] = transformed_events
            
            logger.info(f"Successfully received {len(peaks)} peaks from API")
            return peaks, payload, integration_params
        elif isinstance(result, list):
            peaks = result
            logger.info(f"Successfully received {len(peaks)} peaks from API")
            return peaks, payload, {}
        elif isinstance(result, dict) and "peaks" in result:
            # Fallback for old format if needed
            peaks = result["peaks"]
            logger.info(f"Successfully received {len(peaks)} peaks from API (legacy format)")
            return peaks, payload, {}
        else:
            logger.error(f"Unexpected API response format: {type(result)}")
            return None, payload, None
            
    except requests.exceptions.ConnectionError:
        logger.error(f"Could not connect to API at {api_url}. Make sure the mock API server is running.")
        return None, None, None
    except requests.exceptions.Timeout:
        logger.error(f"API request timed out after 30 seconds")
        return None, None, None
    except requests.exceptions.RequestException as e:
        logger.error(f"API Request Error: {e}")
        return None, None, None
    except json.JSONDecodeError as e:
        logger.error(f"JSON Decode Error: {e}")
        return None, None, None
    except Exception as e:
        logger.error(f"Unexpected Error: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def calculate_baseline_coordinates(chromatogram_data: Dict[str, Any], start_time: float, end_time: float) -> Optional[Dict[str, Any]]:
    """
    Calculate the coordinates (x,y) for baseline start and end points, and compute slope and offset.
    
    Parameters:
    -----------
    chromatogram_data : Dict[str, Any]
        Dictionary containing:
        - time_steps: list of float (x values)
        - signal: list of float (y values)
    start_time : float
        Start time for baseline calculation
    end_time : float
        End time for baseline calculation
        
    Returns:
    --------
    Optional[Dict[str, Any]]
        Dictionary containing:
        - start_coords: tuple (x, y) for start point
        - end_coords: tuple (x, y) for end point
        - slope: float
        - offset: float
        Returns None if calculation fails
    """
    try:
        if not isinstance(chromatogram_data, dict):
            logger.error("Invalid chromatogram data format")
            return None
            
        time_steps = chromatogram_data.get("time_steps", [])
        signal = chromatogram_data.get("signal", [])
        
        if not time_steps or not signal or len(time_steps) != len(signal):
            logger.error("Invalid or empty chromatogram data")
            return None
            
        # Convert to numpy arrays for easier manipulation
        time_array = np.array(time_steps)
        signal_array = np.array(signal)
        
        # Find closest indices for start and end times
        start_idx = np.argmin(np.abs(time_array - start_time))
        end_idx = np.argmin(np.abs(time_array - end_time))
        
        # Get the actual coordinates
        start_x = time_array[start_idx]
        start_y = signal_array[start_idx]
        end_x = time_array[end_idx]
        end_y = signal_array[end_idx]
        
        # Calculate slope and offset
        if end_x != start_x:
            slope = (end_y - start_y) / (end_x - start_x)
            offset = start_y - slope * start_x
        else:
            slope = 0.0
            offset = start_y
            
        result = {
            "start_coords": (start_x, start_y),
            "end_coords": (end_x, end_y),
            "slope": slope,
            "offset": offset
        }
        
        # Print to console as requested
        print(f"Baseline Calculation Results:")
        print(f"Start Point: ({start_x:.6f}, {start_y:.6f})")
        print(f"End Point: ({end_x:.6f}, {end_y:.6f})")
        print(f"Slope: {slope:.6f}")
        print(f"Offset: {offset:.6f}")
        
        # Validate baseline sanity
        is_valid = validate_baseline_sanity(
            time_array, signal_array, start_time, end_time, slope, offset
        )
        result["is_valid"] = is_valid
        
        return result
        
    except Exception as e:
        logger.error(f"Error calculating baseline coordinates: {e}")
        return None

def validate_baseline_sanity(time_array: np.ndarray, signal_array: np.ndarray, 
                           start_time: float, end_time: float, slope: float, offset: float,
                           max_points_under_baseline: int = 10) -> bool:
    """
    Validate if a baseline is reasonable by checking how many signal points are below the baseline line.
    
    Parameters:
    -----------
    time_array : np.ndarray
        Array of time values
    signal_array : np.ndarray
        Array of signal values
    start_time : float
        Start time of baseline
    end_time : float
        End time of baseline
    slope : float
        Slope of baseline line
    offset : float
        Offset of baseline line
    max_points_under_baseline : int
        Maximum allowed points below baseline before considering invalid
        
    Returns:
    --------
    bool
        True if baseline is valid, False if invalid
    """
    try:
        # Find indices for the time window
        start_idx = np.where(time_array >= start_time)[0]
        end_idx = np.where(time_array <= end_time)[0]
        
        if len(start_idx) == 0 or len(end_idx) == 0:
            print("Invalid time range for baseline validation")
            return False
            
        bs_id = start_idx[0]  # First index >= start_time
        be_id = end_idx[-1]   # Last index <= end_time
        
        if bs_id >= be_id:
            print("Invalid time range: start >= end")
            return False
        
        # Get signal values in the baseline window
        window_signal = signal_array[bs_id:be_id+1]
        window_time = time_array[bs_id:be_id+1]
        
        # Calculate baseline line values for the same time points
        # Using the line equation: y = slope * x + offset
        baseline_values = slope * window_time + offset
        
        # Calculate how many points are below the baseline (signal < baseline)
        points_under = np.sum(window_signal < baseline_values)
        
        print(f"Baseline Validation:")
        print(f"  Time window: {start_time:.3f} - {end_time:.3f}")
        print(f"  Points in window: {len(window_signal)}")
        print(f"  Points below baseline: {points_under}")
        print(f"  Max allowed under: {max_points_under_baseline}")
        
        if points_under > max_points_under_baseline:
            print("  Result: Invalid")
            return False
        else:
            print("  Result: Valid")
            return True
            
    except Exception as e:
        logger.error(f"Error validating baseline sanity: {e}")
        print("  Result: Invalid (error occurred)")
        return False
