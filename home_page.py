import dash
from dash import dcc, html, dash_table

MIN_START = 0.5  # Define MIN_START, or import from a central config if preferred

INITIAL_PARAMS = {
    "MinimumArea": 2289.0,
    "MinimumHeight": 10.0,
    "IntegrationStart": MIN_START,
    "IntegrationEnd": 48.0,
    "PeakWidth": 20.0,
    "DetectionThreshold": 20.0,
    "LiftoffPct": 5.0,
    "TouchdownPct": 0.0,
}

layout = html.Div([

    html.H1("Calculate Peak Table as Empower", className="app-title"),
    # File Upload Section (Copied from original app.layout)
    html.Div([
        html.Div([
            # Sample file upload
            html.Div([
                html.Label("Load Sample Data"),
                html.Div([
                    dcc.Upload(
                        id='upload-sample',
                        children=html.Div(['Select excel/csv/CH2/cdf... ']),
                        accept='.xlsx,.xls,.csv,.CH2,.cdf',
                        className="upload-box"
                    ),
                    html.Button(
                        "🗄️",
                        id='sample-cloud-btn',
                        className="upload-icon-btn",
                        title="Upload from server"
                    ),
                ], className="upload-with-download"),
            ], className="upload-item"),

            # Background file upload
            html.Div([
                html.Label("Load Background Data"),
                html.Div([
                    dcc.Upload(
                        id='upload-background',
                        children=html.Div(['Select excel/csv/CH2/cdf... ']),
                        accept='.xlsx,.xls,.csv,.CH2,.cdf',
                        className="upload-box"
                    ),
                    html.Button(
                        "🗄️",
                        id='background-cloud-btn',
                        className="upload-icon-btn",
                        title="Upload from server"
                    ),
                ], className="upload-with-download"),
            ], className="upload-item"),

            # Peak Table file upload
            html.Div([
                html.Label("Upload Peak Table"),
                html.Div([
                    dcc.Upload(
                        id='upload-peaks',
                        children=html.Div(['Select or drop excel file... ']),
                        className="upload-box"
                    ),
                    html.Button(
                        "⬇️",
                        id='download-demo-peaks-btn',
                        className="download-icon-btn",
                        title="Download demo file"
                    ),
                ], className="upload-with-download"),
            ], className="upload-item"),

            # Global Parameters file upload
            html.Div([
                html.Label("Upload Global Parameters"),
                html.Div([
                    dcc.Upload(
                        id='upload-params',
                        children=html.Div(['Select or drop excel file... ']),
                        className="upload-box"
                    ),
                    html.Button(
                        "⬇️",
                        id='download-demo-params-btn',
                        className="download-icon-btn",
                        title="Download demo file"
                    ),
                ], className="upload-with-download"),
            ], className="upload-item"),

            # Integration Events file upload
            html.Div([
                html.Label("Upload Integration Events"),
                html.Div([
                    dcc.Upload(
                        id='upload-events',
                        children=html.Div(['Select or drop excel file... ']),
                        className="upload-box"
                    ),
                    html.Button(
                        "⬇️",
                        id='download-demo-events-btn',
                        className="download-icon-btn",
                        title="Download demo file"
                    ),
                ], className="upload-with-download"),
            ], className="upload-item"),
        ], className="upload-grid"),
    ], className="file-uploads"),

    # Upload status message
    html.Div(id='upload-status', style={'textAlign': 'center'}),

    # Main Content (2-column layout) - Copied from original app.layout
    html.Div([
        # Left Column (Chromatogram and Peak Table)
        html.Div([
            # Chromatogram Panel
            html.Div([
                html.Div([
                    html.Button('Reset Zoom', id='reset-button', className="action-button"),
                ], className="buttons"),

                dcc.Graph(
                    id='chromatogram-plot',
                    className="plot-container",
                    config={
                        'displayModeBar': True,
                        'modeBarButtonsToAdd': ['select2d'],
                        'scrollZoom': True,
                        'doubleClick': 'reset',
                        'showAxisDragHandles': True,
                        'displaylogo': False,
                    }
                ),
                html.Div([
                    html.P("Click the 'Box Select' button in the toolbar, then drag to select an area to zoom in.",
                           style={'textAlign': 'center', 'color': '#666'})
                ]),
            ], className="panel"),

            # Peak Table Panel
            html.Div([
                html.Div([
                    html.Div("Peak Table", className="panel-title"),
                    html.Div([
                        html.Button('Refresh Peaks', id='refresh-peaks-button', className="action-button"),
                        html.Button('Add New Peak', id='add-row-button', className="action-button"),
                        html.Button('Download Peak Table', id='download-button', className="download-btn"),
                    ], className="peak-table-buttons"),
                ], className="peak-table-header"),
                dcc.Loading(
                    id="peaks-loading",
                    type="circle",
                    children=[
                        html.Div(id='peaks-api-status', style={'textAlign': 'center', 'margin': '10px 0'})
                    ]
                ),
                html.Div(id='peaks-upload-status', style={'textAlign': 'center'}),
                html.Div(id='peaks-table-container', className="table-container"),
                dcc.Download(id="download-peaks-csv"),
            ], className="panel"),
        ], className="left-column"),

        # Right Column (Parameters and Events) - Copied from original app.layout
        html.Div([
            # Integration Parameters Card
            html.Div([
                html.Div([
                    html.H2("Integration Parameters"),
                    html.Button('Download', id='download-params-btn', className="download-btn"),
                ], className="card-header"),

                html.Div([
                    # Minimum Area
                    html.Div([
                        html.Span("Minimum Area:", className="parameter-name"),
                        html.Span([
                            dcc.Slider(
                                id="minAreaSlider",
                                min=0,
                                max=5000,
                                value=INITIAL_PARAMS["MinimumArea"],
                                marks=None,
                                className="parameter-slider"
                            ),
                            dcc.Input(
                                id="minArea",
                                type="text",
                                value=str(INITIAL_PARAMS["MinimumArea"]),
                                className="parameter-input",
                                debounce=True,
                                n_submit=0,
                                n_blur=0
                            ),
                            html.Span("μV*sec", className="unit"),
                        ], className="parameter-value"),
                    ], className="parameter-row"),

                    # Minimum Height
                    html.Div([
                        html.Span("Minimum Height:", className="parameter-name"),
                        html.Span([
                            dcc.Slider(
                                id="minHeightSlider",
                                min=0,
                                max=100,
                                value=INITIAL_PARAMS["MinimumHeight"],
                                marks=None,
                                className="parameter-slider"
                            ),
                            dcc.Input(
                                id="minHeight",
                                type="text",
                                value=str(INITIAL_PARAMS["MinimumHeight"]),
                                className="parameter-input",
                                debounce=True,
                                n_submit=0,
                                n_blur=0
                            ),
                            html.Span("μV", className="unit"),
                        ], className="parameter-value"),
                    ], className="parameter-row"),

                    # Integration Start
                    html.Div([
                        html.Span("Integration Start:", className="parameter-name"),
                        html.Span([
                            dcc.Slider(
                                id="integrationStartSlider",
                                min=MIN_START,
                                max=60,
                                step=0.1,
                                value=INITIAL_PARAMS["IntegrationStart"],
                                marks=None,
                                className="parameter-slider"
                            ),
                            dcc.Input(
                                id="integrationStart",
                                type="text",
                                value=str(INITIAL_PARAMS["IntegrationStart"]),
                                className="parameter-input",
                                debounce=True,
                                n_submit=0,
                                n_blur=0,
                                min=MIN_START
                            ),
                            html.Span("min", className="unit"),
                        ], className="parameter-value"),
                    ], className="parameter-row"),

                    # Integration End
                    html.Div([
                        html.Span("Integration End:", className="parameter-name"),
                        html.Span([
                            dcc.Slider(
                                id="integrationEndSlider",
                                min=0,
                                max=60,
                                step=0.1,
                                value=INITIAL_PARAMS["IntegrationEnd"],
                                marks=None,
                                className="parameter-slider"
                            ),
                            dcc.Input(
                                id="integrationEnd",
                                type="text",
                                value=str(INITIAL_PARAMS["IntegrationEnd"]),
                                className="parameter-input",
                                debounce=True,
                                n_submit=0,
                                n_blur=0
                            ),
                            html.Span("min", className="unit"),
                        ], className="parameter-value"),
                    ], className="parameter-row"),

                    # Peak Width
                    html.Div([
                        html.Span("Peak Width:", className="parameter-name"),
                        html.Span([
                            dcc.Slider(
                                id="peakWidthSlider",
                                min=0,
                                max=60,
                                step=0.1,
                                value=INITIAL_PARAMS["PeakWidth"],
                                marks=None,
                                className="parameter-slider"
                            ),
                            dcc.Input(
                                id="peakWidth",
                                type="text",
                                value=str(INITIAL_PARAMS["PeakWidth"]),
                                className="parameter-input",
                                debounce=True,
                                n_submit=0,
                                n_blur=0
                            ),
                            html.Span("sec", className="unit"),
                        ], className="parameter-value"),
                    ], className="parameter-row"),

                    # Detection Threshold
                    html.Div([
                        html.Span("Detection Threshold:", className="parameter-name"),
                        html.Span([
                            dcc.Slider(
                                id="detectionThresholdSlider",
                                min=0,
                                max=100,
                                step=0.1,
                                value=INITIAL_PARAMS["DetectionThreshold"],
                                marks=None,
                                className="parameter-slider"
                            ),
                            dcc.Input(
                                id="detectionThreshold",
                                type="text",
                                value=str(INITIAL_PARAMS["DetectionThreshold"]),
                                className="parameter-input",
                                debounce=True,
                                n_submit=0,
                                n_blur=0
                            ),
                            html.Span("μV", className="unit"),
                        ], className="parameter-value"),
                    ], className="parameter-row"),

                    # Liftoff %
                    html.Div([
                        html.Span("Liftoff %:", className="parameter-name"),
                        html.Span([
                            dcc.Slider(
                                id="liftoffSlider",
                                min=0,
                                max=100,
                                step=0.1,
                                value=INITIAL_PARAMS["LiftoffPct"],
                                marks=None,
                                className="parameter-slider"
                            ),
                            dcc.Input(
                                id="liftoff",
                                type="text",
                                value=str(INITIAL_PARAMS["LiftoffPct"]),
                                className="parameter-input",
                                debounce=True,
                                n_submit=0,
                                n_blur=0
                            ),
                            html.Span("%", className="unit"),
                        ], className="parameter-value"),
                    ], className="parameter-row"),

                    # Touchdown %
                    html.Div([
                        html.Span("Touchdown %:", className="parameter-name"),
                        html.Span([
                            dcc.Slider(
                                id="touchdownSlider",
                                min=0,
                                max=100,
                                step=0.1,
                                value=INITIAL_PARAMS["TouchdownPct"],
                                marks=None,
                                className="parameter-slider"
                            ),
                            dcc.Input(
                                id="touchdown",
                                type="text",
                                value=str(INITIAL_PARAMS["TouchdownPct"]),
                                className="parameter-input",
                                debounce=True,
                                n_submit=0,
                                n_blur=0
                            ),
                            html.Span("%", className="unit"),
                        ], className="parameter-value"),
                    ], className="parameter-row"),
                ], className="parameter-group"),
            ], className="parameters-card"),

            # Integration Events Card - Copied from original app.layout
            html.Div([
                html.Div([
                    html.Div("Integration Events", className="panel-title"),
                    html.Div([
                        html.Button('Add New Event', id='add-event-btn', className="action-button"),
                        html.Button('Download', id='download-events-btn', className="download-btn"),
                    ], className="peak-table-buttons"),
                ], className="peak-table-header"),

                html.Div(id='events-table-container', children=[
                    dash_table.DataTable(
                        id='events-data-table',
                        columns=[
                            {"name": "Time (min)", "id": "Time", "type": "numeric", "format": {"specifier": ".3f"}, "deletable": False, "editable": True},
                            {"name": "Type", "id": "Type", "type": "text", "deletable": False, "editable": True},
                            {"name": "Value", "id": "Value", "type": "numeric", "format": {"specifier": ".1f"}, "deletable": False, "editable": True},
                            {"name": "Stop (min)", "id": "Stop", "type": "numeric", "format": {"specifier": ".3f"}, "deletable": False, "editable": True},
                        ],
                        data=[],
                        editable=True,
                        row_deletable=True,
                        style_table={'width': '100%'},
                        style_cell={
                            'textAlign': 'left',
                            'padding': '8px',
                            'minWidth': '80px',
                            'width': '120px',
                            'maxWidth': '180px',
                            'whiteSpace': 'normal',
                            'height': 'auto',
                        },
                        style_header={
                            'backgroundColor': 'rgb(230, 230, 230)',
                            'fontWeight': 'bold'
                        },
                        style_data_conditional=[
                            {
                                'if': {'row_index': 'odd'},
                                'backgroundColor': 'rgb(248, 248, 248)'
                            }
                        ],
                        page_size=10,
                        page_action='native'
                    )
                ], className="events-table-container"),
            ], className="panel"),
        ], className="side-panel")
    ], className="main-content"),

    # Store components (Copied from original app.layout)
    dcc.Store(id='stored-sample-data'),
    dcc.Store(id='stored-background-data'),
    dcc.Store(id='stored-peaks-data'),
    dcc.Store(id='stored-params-data', data=[INITIAL_PARAMS]),
    dcc.Store(id='stored-events-data'),
    dcc.Store(id='graph-zoom-state'),
    dcc.Store(id='table-page-state', data=0),
    dcc.Store(id='api-result-store'),
    dcc.Store(id='upload-state', data={'status': 'idle', 'message': ''}),
    dcc.Interval(id='upload-status-checker', interval=500, n_intervals=0),

    # Download components (Copied from original app.layout)
    dcc.Download(id="download-params-csv"),
    dcc.Download(id="download-events-csv"),
    dcc.Download(id="download-demo-peaks"),
    dcc.Download(id="download-demo-params"),
    dcc.Download(id="download-demo-events"),

    # Hidden div for storing current box selection (Copied from original app.layout)
    html.Div(id='selected-data', style={'display': 'none'}),

    # Cloud Upload Modal (Copied from original app.layout)
    html.Div(id='cloud-upload-modal', className='modal', style={'display': 'none'}, children=[
        html.Div(className='modal-content', children=[
            html.H3(id='cloud-upload-title', children="Cloud Upload Configuration", style={'textAlign': 'center'}),
            html.Div([
                html.Label("Project Name"),
                dcc.Input(
                    id='project-name-input',
                    type='text',
                    value='',
                    placeholder='Enter project path...',
                    style={'width': '100%', 'marginBottom': '15px'}
                ),
                html.Label("Channel ID"),
                dcc.Input(
                    id='channel-id-input',
                    type='text',
                    value='',
                    placeholder='Enter channel ID...',
                    style={'width': '100%', 'marginBottom': '15px'}
                ),
                html.Div(id='cloud-form-error', className='error-message'),
                html.Div(id='cloud-upload-error', className='error-message', style={'color': 'red', 'marginBottom': '15px'}),
                html.Div([
                    html.Button('Cancel', id='cloud-cancel-btn', className='modal-btn'),
                    html.Button('Submit', id='cloud-submit-btn', className='modal-btn primary')
                ], style={'display': 'flex', 'justifyContent': 'flex-end', 'gap': '10px'}),
                html.Div(id='cloud-upload-type', style={'display': 'none'})
            ], style={'padding': '20px'})
        ])
    ]),

    # Cloud Loading Modal (Copied from original app.layout)
    html.Div(id='cloud-loading-modal', className='modal', style={'display': 'none'}, children=[
        html.Div(className='modal-content', children=[
            html.H3("Loading Data", style={'textAlign': 'center'}),
            html.Div([
                html.Div(className='loading-spinner', style={'margin': '0 auto', 'width': '50px', 'height': '50px'}),
                html.Div(id='cloud-loading-status', style={'textAlign': 'center', 'marginTop': '20px', 'color': '#666'})
            ], style={'padding': '20px'})
        ])
    ]),

    # Interval component to check for selections (Copied from original app.layout)
    dcc.Interval(id='selection-checker', interval=500, disabled=False),

    # Peak Modal
    html.Div(id='add-peak-modal', className='modal', style={'display': 'none'}, children=[
        html.Div(className='modal-content', children=[
            html.H2('Add New Peak'),

            html.Label('Peak Name:'),
            dcc.Input(id='peak-name-input', type='text', className="modal-input"),

            html.Label('Integration Type:'),
            dcc.Input(id='peak-inttype-input', type='text', className="modal-input"),

            html.Label('Retention Time (min):'),
            dcc.Input(id='peak-rt-input', type='number', className="modal-input"),

            html.Label('Start Time (min): *Required'),
            dcc.Input(id='peak-start-input', type='number', required=True, className="modal-input"),

            html.Label('End Time (min): *Required'),
            dcc.Input(id='peak-end-input', type='number', required=True, className="modal-input"),

            html.Label('Slope:'),
            dcc.Input(id='peak-slope-input', type='text', className="modal-input"),

            html.Label('Offset:'),
            dcc.Input(id='peak-offset-input', type='text', className="modal-input"),

            html.Div(id='peak-form-error', className='error-message'),
            
            html.Div([
                html.Button('Cancel', id='cancel-peak-button', className="modal-button cancel-button"),
                html.Button('Save Peak', id='save-peak-button', className="modal-button submit-button")
            ], className="modal-buttons")
        ])
    ]),

    # Event Modal (Copied from original app.layout)
    html.Div(id='add-event-modal', className='modal', style={'display': 'none'}, children=[
        html.Div(className='modal-content', children=[
            html.H3("Add New Event", style={'textAlign': 'center'}),
            html.Div([
                html.Label("Time (min) *"),
                dcc.Input(id='event-time-input', type='number', value=None, step=0.001),
                html.Label("Type *"),
                dcc.Dropdown(
                    id='event-type-input',
                    options=[], # Define actual options in callbacks or here
                    value=None,
                    placeholder='Select event type'
                ),
                html.Label("Value"),
                dcc.Input(id='event-value-input', type='number', value=None, step=0.01),
                html.Label("Stop (min)"),
                dcc.Input(id='event-stop-input', type='number', value=None, step=0.001),
                html.Div(id='event-form-error', className='error-message'),
                html.Div([
                    html.Button('Cancel', id='cancel-event-button', className="modal-button"),
                    html.Button('Save', id='save-event-button', className="modal-button primary"),
                ], className="modal-buttons"),
            ], className="modal-form"),
        ]),
    ]),

    # Error Modal (Copied from original app.layout)
    html.Div(id='error-modal', className='modal', style={'display': 'none'}, children=[
        html.Div(className='modal-content', children=[
            html.H3("Error", style={'textAlign': 'center', 'color': '#dc3545'}),
            html.Div(id='error-message', style={'marginBottom': '20px'}),
            html.Div([
                html.Button('Close', id='close-error-button', className="modal-button primary"),
            ], className="modal-buttons"),
        ]),
    ]),

    dcc.Interval(
        id='upload-state-reset-interval',
        interval=2000,
        n_intervals=0,
        disabled=True
    ),
    html.Div(id='feedback-save-status', style={'display': 'none'}),
]) 