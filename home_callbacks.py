from dash import Dash, Input, Output, State, html, dash_table, dcc, callback_context
import dash
import pandas as pd
import base64
import datetime
import io
import os
import tempfile
import json
import numpy as np
from typing import Dict, Any, List, Optional, Union, Tuple
import logging
import time
import traceback
import psycopg2
import psycopg2.extras
from db_utils import get_db_connection, get_task_feedbacks, get_sample_source_info
from utils import (
    read_CH2, read_cdf, parse_excel, parse_csv,
    process_peaktable_api, format_exponential,
    process_parameter_excel, process_events_excel,
    save_peaktable_to_db, event_type_options,
    save_peaktable_feedback, read_project_channel,
)
from graph_utils import generate_sample_figure, create_figure

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('callbacks')

col_names = {
    "Name": "Name",
    "IntType": "Integration Type",
    "RetentionTime": "Retention Time (min)",
    "StartTime": "Start Time",
    "EndTime": "End Time",
    "Slope": "Slope",
    "Offset": "Offset",
}

MIN_START = 0.5
DEFAULT_PROJECT_NAME = '202407\\Clinical_Project\\C18053047'
DEFAULT_SAMPLE_CHANNEL = '1522'
DEFAULT_BACKGROUND_CHANNEL = '1502'

def register_home_callbacks(app: Dash):
    """Registers all callbacks for the home page (Calculate Peak Table)."""
    # Client-side callback to immediately hide the cloud upload modal and show loading modal when Submit is clicked
    app.clientside_callback(
        """
        function(n_clicks) {
            if (n_clicks) {
                return [{'display': 'none'}, {'display': 'block'}, 'Loading data from Empower...'];
            }
            return [dash_clientside.no_update, dash_clientside.no_update, dash_clientside.no_update];
        }
        """,
        [Output('cloud-upload-modal', 'style', allow_duplicate=True),
         Output('cloud-loading-modal', 'style', allow_duplicate=True),
         Output('cloud-loading-status', 'children', allow_duplicate=True)],
        Input('cloud-submit-btn', 'n_clicks'),
        prevent_initial_call=True
    )
    # Client-side callback to hide the loading modal when sample or background data is received
    app.clientside_callback(
        """
        function(sample_data, background_data) {
            if (sample_data || background_data) {
                setTimeout(function() {
                    document.getElementById('cloud-loading-modal').style.display = 'none';
                }, 500);
                return {'display': 'none'};
            }
            return dash_clientside.no_update;
        }
        """,
        Output('cloud-loading-modal', 'style', allow_duplicate=True),
        [Input('stored-sample-data', 'data'),
         Input('stored-background-data', 'data')],
        prevent_initial_call=True
    )
    # --- Cloud Upload Modal and State Management Callbacks ---
    @app.callback(
        [Output('cloud-upload-modal', 'style'),
         Output('cloud-upload-type', 'children'),
         Output('channel-id-input', 'value'),
         Output('cloud-upload-title', 'children'),
         Output('project-name-input', 'placeholder'),
         Output('channel-id-input', 'placeholder'),
         Output('project-name-input', 'value')],
        [Input('sample-cloud-btn', 'n_clicks'),
         Input('background-cloud-btn', 'n_clicks'),
         Input('cloud-cancel-btn', 'n_clicks')],
        [State('cloud-upload-modal', 'style')]
    )
    def toggle_cloud_modal(sample_clicks, background_clicks, cancel_clicks, current_style):
        ctx = dash.callback_context
        modal_style = {'display': 'none'}
        upload_type = ""
        channel_value = ""
        modal_title = dash.no_update
        project_placeholder = "Enter project path..."
        channel_placeholder = "Enter channel ID..."
        project_value = ""

        if not ctx.triggered:
            return modal_style, upload_type, channel_value, modal_title, project_placeholder, channel_placeholder, project_value

        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]

        if trigger_id == 'sample-cloud-btn':
            modal_style = {'display': 'block'}
            upload_type = "sample"
            channel_value = ""
            modal_title = "Upload Sample from Empower"
            project_placeholder = DEFAULT_PROJECT_NAME
            channel_placeholder = DEFAULT_SAMPLE_CHANNEL
            project_value = ""
        elif trigger_id == 'background-cloud-btn':
            modal_style = {'display': 'block'}
            upload_type = "background"
            channel_value = ""
            modal_title = "Upload Background from Empower"
            project_placeholder = DEFAULT_PROJECT_NAME
            channel_placeholder = DEFAULT_BACKGROUND_CHANNEL
            project_value = ""
        elif trigger_id == 'cloud-cancel-btn':
            modal_style = {'display': 'none'}
            upload_type = ""
            channel_value = ""
            modal_title = "Cloud Upload Configuration"
            project_placeholder = "Enter project path..."
            channel_placeholder = "Enter channel ID..."
            project_value = ""
        elif current_style:
            modal_style = current_style
            project_placeholder = "Enter project path..."
            channel_placeholder = "Enter channel ID..."
            project_value = ""
            channel_value = ""
        return modal_style, upload_type, channel_value, modal_title, project_placeholder, channel_placeholder, project_value

    @app.callback(
        [Output('upload-state', 'data'),
         Output('cloud-loading-modal', 'style'),
         Output('cloud-upload-modal', 'style', allow_duplicate=True),
         Output('cloud-loading-status', 'children', allow_duplicate=True)],
        [Input('cloud-submit-btn', 'n_clicks'),
         Input('stored-sample-data', 'data'),
         Input('stored-background-data', 'data'),
         Input('upload-status-checker', 'n_intervals')],
        [State('upload-state', 'data'),
         State('cloud-loading-modal', 'style'),
         State('cloud-upload-modal', 'style'),
         State('cloud-upload-type', 'children')],
        prevent_initial_call=True
    )
    def manage_upload_state(submit_clicks, sample_data, background_data, n_intervals,
                          current_state, loading_style, modal_style, upload_type):
        ctx = dash.callback_context
        if not ctx.triggered:
            if current_state and current_state.get('status') == 'uploading':
                 return {'status': 'idle', 'message': ''}, {'display': 'none'}, dash.no_update, ""
            return dash.no_update, dash.no_update, dash.no_update, dash.no_update
        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
        new_state = current_state if current_state else {
            'status': 'idle', 'message': '', 'start_time': None, 'data_received': False
        }
        output_loading_style = dash.no_update
        output_modal_style = dash.no_update
        output_loading_status = dash.no_update
        if trigger_id == 'cloud-submit-btn' and submit_clicks:
            loading_message = "Loading data from Empower..."
            if upload_type == "sample":
                loading_message = "Loading sample data from Empower..."
            elif upload_type == "background":
                loading_message = "Loading background data from Empower..."
            new_state = {
                'status': 'uploading',
                'message': loading_message,
                'start_time': time.time(),
                'data_received': False
            }
            output_loading_style = {'display': 'block'}
            output_modal_style = {'display': 'none'}
            output_loading_status = loading_message
        elif trigger_id in ['stored-sample-data', 'stored-background-data']:
             if new_state.get('status') == 'uploading' and (sample_data or background_data):
                 new_state = {
                     'status': 'complete',
                     'message': 'Upload complete!',
                     'start_time': new_state.get('start_time'),
                     'finish_time': time.time(),
                     'data_received': True
                 }
                 output_loading_style = {'display': 'none'}
                 output_modal_style = {'display': 'none'}
                 output_loading_status = new_state['message']
        elif trigger_id == 'upload-status-checker':
            if new_state.get('status') == 'uploading':
                elapsed_time = time.time() - new_state.get('start_time', time.time())
                TIMEOUT_SECONDS = 30
                if elapsed_time > TIMEOUT_SECONDS and not new_state.get('data_received', False):
                    new_state = {
                        'status': 'error',
                        'message': f'Upload timed out after {TIMEOUT_SECONDS} seconds. Please check connection or parameters.',
                        'start_time': new_state.get('start_time'),
                        'finish_time': time.time(),
                        'data_received': False
                    }
                    output_loading_style = {'display': 'none'}
                    output_modal_style = {'display': 'none'}
                    output_loading_status = new_state['message']
                else:
                     output_loading_status = new_state.get('message', 'Loading...')
                     output_loading_style = {'display': 'block'}
        final_state = new_state if new_state != current_state else dash.no_update
        return final_state, output_loading_style, output_modal_style, output_loading_status

    @app.callback(
        [Output('cloud-loading-status', 'children', allow_duplicate=True),
         Output('cloud-loading-modal', 'style', allow_duplicate=True)],
        Input('upload-state', 'data'),
        prevent_initial_call=True
    )
    def update_loading_display_from_state(current_state):
        if not current_state:
            return "", {'display': 'none'}
        status = current_state.get('status', 'idle')
        message = current_state.get('message', '')
        if status == 'uploading':
            return message, {'display': 'block'}
        elif status == 'complete' or status == 'error':
            return message, {'display': 'none'}
        else:
            return "", {'display': 'none'}

    @app.callback(
        Output('cloud-upload-error', 'children'),
        [Input('cloud-submit-btn', 'n_clicks')],
        [State('project-name-input', 'value'),
         State('channel-id-input', 'value'),
         State('cloud-upload-type', 'children')],
        prevent_initial_call=True
    )
    def validate_cloud_inputs(submit_clicks, project_name_in, channel_id_in, upload_type):
        if not submit_clicks:
            return ""
        project_name = project_name_in if project_name_in else DEFAULT_PROJECT_NAME
        channel_id = channel_id_in
        if not channel_id:
            if upload_type == 'sample':
                channel_id = DEFAULT_SAMPLE_CHANNEL
            elif upload_type == 'background':
                channel_id = DEFAULT_BACKGROUND_CHANNEL
        if not project_name:
            return "Error: Project name is required"
        if not channel_id:
            return "Error: Channel ID is required"
        try:
            int(channel_id)
        except (ValueError, TypeError):
            return f"Error: Channel ID must be a number (Value: '{channel_id}')"
        return ""

    @app.callback(
        [Output('upload-status', 'children', allow_duplicate=True),
         Output('stored-sample-data', 'data', allow_duplicate=True),
         Output('stored-background-data', 'data', allow_duplicate=True),
         Output('stored-peaks-data', 'data', allow_duplicate=True),
         Output('peaks-api-status', 'children', allow_duplicate=True),
         Output('upload-sample', 'contents', allow_duplicate=True)],
        [Input('cloud-submit-btn', 'n_clicks')],
        [State('project-name-input', 'value'),
         State('channel-id-input', 'value'),
         State('cloud-upload-type', 'children')],
        prevent_initial_call=True
    )
    def handle_cloud_upload(submit_clicks, project_name_in, channel_id_in, upload_type):
        if not submit_clicks:
            return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        project_name = project_name_in if project_name_in else DEFAULT_PROJECT_NAME
        channel_id = channel_id_in
        if not channel_id:
            if upload_type == 'sample':
                channel_id = DEFAULT_SAMPLE_CHANNEL
            elif upload_type == 'background':
                channel_id = DEFAULT_BACKGROUND_CHANNEL
            else:
                 return "Error: Unknown upload type.", dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        if not project_name or not channel_id:
            return "Error: Project name and Channel ID are required.", dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        try:
            import types
            if not hasattr(dash.callback_context, "_chrome_app_project_name"):
                dash.callback_context._chrome_app_project_name = project_name
            else:
                setattr(dash.callback_context, "_chrome_app_project_name", project_name)
            channel_num = int(channel_id)
            print(f"Attempting to read project: {project_name}, channel: {channel_num} (Type: {upload_type})")
            df = read_project_channel(project_name, channel_num)
            if df is None:
                error_msg = f"Error: No data found for project '{project_name}' channel {channel_num}"
                print(error_msg)
                return error_msg, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
            if len(df.columns) < 2:
                error_msg = "Error: Invalid data format - missing required time/signal columns"
                print(error_msg)
                return error_msg, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
            data_type = "(Sample)" if upload_type == "sample" else "(Background)"
            legend_name = f"Channel {channel_num} {data_type}"
            print(f"Formatting data with legend: {legend_name}")
            formatted_data = {
                "time_steps": df.iloc[:, 0].tolist(),
                "signal": df.iloc[:, 1].tolist(),
                "columns": ["Time (min)", legend_name]
            }
            if upload_type == "sample":
                print("Updating stored-sample-data")
                return "", formatted_data, dash.no_update, None, "", None
            else:
                print("Updating stored-background-data")
                return "", dash.no_update, formatted_data, dash.no_update, dash.no_update, dash.no_update
        except ValueError:
            error_msg = f"Error: Channel ID must be a number (Value received: '{channel_id}')"
            print(error_msg)
            return error_msg, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
        except Exception as e:
            error_msg = f"Error: Failed to fetch or process data - {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            return error_msg, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update

    @app.callback(
        Output('upload-state', 'data', allow_duplicate=True),
        Input('upload-state-reset-interval', 'n_intervals'),
        State('upload-state', 'data'),
        prevent_initial_call=True
    )
    def auto_reset_upload_state(n_intervals: int, state: dict) -> dict:
        if not state:
            return dash.no_update
        if state.get('status') in ['complete', 'error']:
            return {'status': 'idle', 'message': ''}
        return dash.no_update

    @app.callback(
        Output('upload-state', 'data', allow_duplicate=True),
        Input('upload-status-checker', 'n_intervals'),
        State('upload-state', 'data'),
        prevent_initial_call=True
    )
    def check_upload_timeout(n_intervals, current_state):
        if not current_state or current_state.get('status') != 'uploading':
            return dash.no_update
        start_time = current_state.get('start_time')
        if not start_time:
            return dash.no_update
        UPLOAD_TIMEOUT = 60
        if time.time() - start_time > UPLOAD_TIMEOUT:
            print(f"[check_upload_timeout] Upload timeout after {UPLOAD_TIMEOUT}s")
            return {
                'status': 'error',
                'message': f'Error: Upload timed out after {UPLOAD_TIMEOUT} seconds. Please try again.',
                'start_time': start_time,
                'finish_time': time.time(),
                'data_received': False
            }
        return dash.no_update

    # Create/update figure for Calculate Peak Table page
    @app.callback(
        Output('chromatogram-plot', 'figure'),
        [Input('stored-sample-data', 'data'),
         Input('stored-background-data', 'data'),
         Input('stored-peaks-data', 'data'),
         Input('reset-button', 'n_clicks')],
        [State('graph-zoom-state', 'data'),
         State('chromatogram-plot', 'figure'),
         State('upload-sample', 'filename'),
         State('upload-background', 'filename')]
    )
    def update_graph(sample_data, background_data, peaks_data, n_clicks,
                    zoom_state, current_figure, sample_filename, background_filename):
        ctx = dash.callback_context
        trigger = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else None

        # Convert stored data to dataframes if available
        sample_df = None
        if sample_data:
            # Create the DataFrame
            sample_df = pd.DataFrame({
                "time_steps": sample_data["time_steps"],
                "signal": sample_data["signal"]
            })
            # Set column names if available in the data
            if "columns" in sample_data and len(sample_data["columns"]) >= 2:
                sample_df.columns = sample_data["columns"]
                print(f"Using column names from data: {sample_data['columns']}")
            else:
                # Default column names
                sample_df.columns = ["Time (min)", "Sample"]

        background_df = None
        if background_data:
            # Create the DataFrame
            background_df = pd.DataFrame({
                "time_steps": background_data["time_steps"],
                "signal": background_data["signal"]
            })
            # Set column names if available in the data
            if "columns" in background_data and len(background_data["columns"]) >= 2:
                background_df.columns = background_data["columns"]
                print(f"Using column names from data: {background_data['columns']}")
            else:
                # Default column names
                background_df.columns = ["Time (min)", "Background"]

        peaks_df = pd.DataFrame(peaks_data) if peaks_data else None

        # Handle reset button - only reset if specifically clicked
        reset = trigger == 'reset-button'

        # Check if we're updating due to peaks changing
        peaks_changed = trigger == 'stored-peaks-data'

        # If we're updating due to peaks change (add/delete) and have a current figure
        # keep the current axes ranges
        current_x_range = None
        current_y_range = None

        if peaks_changed and current_figure and 'layout' in current_figure:
            if 'xaxis' in current_figure['layout'] and 'range' in current_figure['layout']['xaxis']:
                current_x_range = current_figure['layout']['xaxis']['range']
            if 'yaxis' in current_figure['layout'] and 'range' in current_figure['layout']['yaxis']:
                current_y_range = current_figure['layout']['yaxis']['range']

        # Create figure if data is available
        if sample_df is not None or background_df is not None:
            # Only pass filenames if they exist and the data doesn't come from SMB
            use_sample_filename = (sample_filename and sample_df is not None and
                                 not sample_df.columns[1].startswith('Channel'))
            use_bg_filename = (background_filename and background_df is not None and
                             not background_df.columns[1].startswith('Channel'))

            fig = create_figure(
                sample_df,
                background_df,
                peaks_df,
                reset=reset,
                sample_filename=sample_filename if use_sample_filename else None,
                background_filename=background_filename if use_bg_filename else None
            )

            # Apply zoom state when appropriate
            # If peaks changed and we have current ranges, apply them
            if peaks_changed and current_x_range and current_y_range:
                fig['layout']['xaxis']['range'] = current_x_range
                fig['layout']['xaxis']['autorange'] = False
                fig['layout']['yaxis']['range'] = current_y_range
                fig['layout']['yaxis']['autorange'] = False
            # Otherwise, use stored zoom state unless reset was clicked
            elif zoom_state and not reset and trigger != 'stored-sample-data' and trigger != 'stored-background-data':
                if 'xaxis.range[0]' in zoom_state and 'xaxis.range[1]' in zoom_state:
                    fig['layout']['xaxis']['range'] = [zoom_state['xaxis.range[0]'], zoom_state['xaxis.range[1]']]
                    fig['layout']['xaxis']['autorange'] = False
                if 'yaxis.range[0]' in zoom_state and 'yaxis.range[1]' in zoom_state:
                    fig['layout']['yaxis']['range'] = [zoom_state['yaxis.range[0]'], zoom_state['yaxis.range[1]']]
                    fig['layout']['yaxis']['autorange'] = False
            return fig

        # Initial load with no data - return sample figure
        return generate_sample_figure()

    @app.callback(
        [Output('peaks-upload-status', 'children'),
         Output('peaks-table-container', 'children'),
         Output('stored-peaks-data', 'data', allow_duplicate=True),
         Output('error-modal', 'style', allow_duplicate=True),
         Output('error-message', 'children', allow_duplicate=True)],
        [Input('upload-peaks', 'contents'),
         Input('save-peak-button', 'n_clicks')],
        [State('upload-peaks', 'filename'),
         State('stored-peaks-data', 'data'),
         State('peak-name-input', 'value'),
         State('peak-inttype-input', 'value'),
         State('peak-rt-input', 'value'),
         State('peak-start-input', 'value'),
         State('peak-end-input', 'value'),
         State('peak-slope-input', 'value'),
         State('peak-offset-input', 'value')],
        prevent_initial_call=True
    )
    def update_peaks_table(contents, save_clicks, filename, stored_data,
                          peak_name, peak_inttype, peak_rt, peak_start, peak_end, peak_slope, peak_offset):
        ctx = dash.callback_context
        trigger = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else None
        status_message = ""

        # Handle file upload
        if trigger == 'upload-peaks' and contents:
            if filename and filename.endswith(('.xlsx', '.xls')):
                df = parse_excel(contents)
            elif filename and filename.endswith('.csv'):
                df = parse_csv(contents)
            else:
                df = None

            if df is None:
                return "", html.Div("Invalid file format"), None, {'display': 'block'}, "Error: Invalid file format"

            # Required columns for peak table
            required_columns = {
                "IntType": "Integration Type",
                "StartTime": "Start Time",
                "EndTime": "End Time"
            }
            optional_columns = {
                "Name": "Name",
                "RetentionTime": "Retention Time",
                "Slope": "Slope",
                "Offset": "Offset"
            }

            # Check required columns
            missing_required = [col for col in required_columns if col not in df.columns]
            if missing_required:
                error_msg = f"Error: Missing required columns: {', '.join([required_columns[col] for col in missing_required])}"
                return "", html.Div(""), None, {'display': 'block'}, error_msg

            # Get available optional columns
            available_columns = ["Name","IntType","RetentionTime"] + ["StartTime", "EndTime"] + ["Slope","Offset"]
            # Ensure columns exist before selecting
            available_columns = [col for col in available_columns if col in df.columns]
            filtered_df = df[available_columns].copy() # Use copy to avoid SettingWithCopyWarning

            # Define numeric columns expected based on full optional list
            numeric_cols = ["RetentionTime", "StartTime", "EndTime", "Slope", "Offset"]
            for col in numeric_cols:
                if col in filtered_df.columns:
                    # Convert to numeric, coercing errors, then fill NA before checking emptiness
                    filtered_df[col] = pd.to_numeric(filtered_df[col], errors='coerce')

            # Remove rows with blank or invalid StartTime or EndTime *after* coercion
            if 'StartTime' in filtered_df.columns and 'EndTime' in filtered_df.columns:
                # Drop rows where StartTime or EndTime could not be converted to numeric (became NaN)
                # or where the original value might have been empty string (which becomes NaN)
                filtered_df = filtered_df.dropna(subset=['StartTime', 'EndTime'])

        # Handle saving new peak data
        elif trigger == 'save-peak-button':
            # Validate required fields
            if peak_start is None or peak_end is None:
                # This validation is handled in the other callback, but we keep it here as a safeguard
                if stored_data is None:
                    # If no existing data and modal save fails validation (shouldn't happen), create empty table
                    filtered_df = pd.DataFrame(columns=["Name","IntType","RetentionTime","StartTime", "EndTime", "Slope","Offset"])
                    status_message = "Error: Missing required fields for new peak." # Provide feedback
                else:
                    filtered_df = pd.DataFrame(stored_data)
            else:
                # Create new peak row with default values for None
                new_peak = {
                    "Name": peak_name if peak_name is not None else "",
                    "IntType": peak_inttype if peak_inttype is not None else "",
                    "RetentionTime": peak_rt if peak_rt is not None else None,
                    "StartTime": peak_start, # Required, should not be None here
                    "EndTime": peak_end,   # Required, should not be None here
                    "Slope": float(peak_slope) if peak_slope is not None and peak_slope != "" else "",
                    "Offset": float(peak_offset) if peak_offset is not None and peak_offset != "" else "",
                    "Comment": "",  # Initialize empty comment for new peaks
                }

                if stored_data is None:
                    # First peak
                    filtered_df = pd.DataFrame([new_peak])
                else:
                    # Add to existing peaks
                    existing_df = pd.DataFrame(stored_data)
                    # Ensure columns match before concat
                    new_peak_df = pd.DataFrame([new_peak], columns=existing_df.columns.intersection(new_peak.keys()))
                    filtered_df = pd.concat([existing_df, new_peak_df], ignore_index=True)

                # Sort by StartTime if it exists
                if 'StartTime' in filtered_df.columns:
                    filtered_df = filtered_df.sort_values(by="StartTime").reset_index(drop=True)

                status_message = "Added new peak"

        # Use existing data if no changes from upload/save and data exists
        elif stored_data is not None:
            filtered_df = pd.DataFrame(stored_data)
        else:
            # If no trigger event modified data and no stored data exists, create an empty table structure
            columns=[
                {"name": "Peak #", "id": "Row", "deletable": False, "editable": False},
                {"name": "Name", "id": "Name", "type": "text", "deletable": False, "editable": True},
                {"name": "Integration Type", "id": "IntType", "type": "text", "deletable": False, "editable": True},
                {"name": "Retention Time (min)", "id": "RetentionTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                {"name": "Start Time", "id": "StartTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                {"name": "End Time", "id": "EndTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                {"name": "Slope", "id": "Slope", "type": "numeric", "format": {"specifier": ".8f"}, "deletable": False, "editable": True},
                {"name": "Offset", "id": "Offset", "type": "numeric", "format": {"specifier": ".8f"}, "deletable": False, "editable": True},
                {"name": "Comment", "id": "Comment", "type": "text", "deletable": False, "editable": True},
            ]
            table = dash_table.DataTable(id='peaks-data-table', columns=columns, data=[], editable=True, row_deletable=True, style_table={'width': '100%'}, style_cell={'textAlign': 'left', 'padding': '8px', 'minWidth': '80px', 'width': '120px', 'maxWidth': '180px', 'whiteSpace': 'normal', 'height': 'auto'}, style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'}, style_data_conditional=[{'if': {'row_index': 'odd'}, 'backgroundColor': 'rgb(248, 248, 248)'}], page_size=10, page_action='native')
            # Return the empty table and clear stored data
            return "", table, None, dash.no_update, dash.no_update

        # Ensure required columns exist even if empty df before creating table
        final_columns = ["Name","IntType","RetentionTime","StartTime", "EndTime", "Slope","Offset"]
        for col in final_columns:
            if col not in filtered_df.columns:
                # Add missing columns with default NA values
                filtered_df[col] = pd.NA if col in ["Name", "IntType"] else pd.NA # Use appropriate NA type

        # Prepare data for table display
        # Add index column starting from 1
        # Make a copy to avoid modifying the DataFrame used for storing
        display_df = filtered_df.copy()
        display_df = display_df.reset_index(drop=True)
        display_df.index = display_df.index + 1  # Start from 1
        display_df = display_df.reset_index().rename(columns={'index': 'Row'})

        # Define table columns structure
        table_columns=[
                {"name": "Peak #", "id": "Row", "deletable": False, "editable": False},
                {"name": "Name", "id": "Name", "type": "text", "deletable": False, "editable": True},
                {"name": "Integration Type", "id": "IntType", "type": "text", "deletable": False, "editable": True},
                {"name": "Retention Time (min)", "id": "RetentionTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                {"name": "Start Time", "id": "StartTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                {"name": "End Time", "id": "EndTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                {"name": "Slope", "id": "Slope", "type": "numeric", "format": {"specifier": ".8f"}, "deletable": False, "editable": True},
                {"name": "Offset", "id": "Offset", "type": "numeric", "format": {"specifier": ".8f"}, "deletable": False, "editable": True},
                {"name": "Comment", "id": "Comment", "type": "text", "deletable": False, "editable": True},
            ]

        # Create the data table
        table = dash_table.DataTable(
            id='peaks-data-table',
            columns=table_columns,
            # Fill NaN values appropriately for JSON serialization before to_dict
            data=display_df.to_dict('records'),
            editable=True,
            row_deletable=True,
            style_table={'width': '100%'},
            style_cell={
                'textAlign': 'left', 'padding': '8px', 'minWidth': '80px', 'width': '120px',
                'maxWidth': '180px', 'whiteSpace': 'normal', 'height': 'auto',
            },
            style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'},
            style_data_conditional=[{'if': {'row_index': 'odd'}, 'backgroundColor': 'rgb(248, 248, 248)'}],
            page_size=10,
            page_action='native',
            page_current=0
        )

        # Prepare data for storage (without the 'Row' index)
        # Ensure stored data uses None for missing optional numerics if appropriate, or empty string for text
        stored_dict = filtered_df.to_dict('records')
        # Convert remaining pandas NAs to None for JSON compatibility
        stored_dict = [{k: (None if pd.isna(v) else v) for k, v in row.items()} for row in stored_dict]

        return status_message, table, stored_dict, dash.no_update, dash.no_update

    @app.callback(
        [Output('upload-status', 'children'),
         Output('stored-sample-data', 'data'),
         Output('stored-peaks-data', 'data', allow_duplicate=True),
         Output('peaks-api-status', 'children', allow_duplicate=True),
         Output('upload-sample', 'contents')],
        Input('upload-sample', 'contents'),
        State('upload-sample', 'filename'),
        prevent_initial_call=True
    )
    def process_sample_upload(contents, filename):
        """Process uploaded sample chromatogram file into API format"""
        print("\n=== Processing Sample Upload ===")
        if contents is None:
            print("No contents provided")
            return "", dash.no_update, dash.no_update, dash.no_update, dash.no_update

        try:
            print(f"Parsing file: {filename}")
            df = None

            if filename.endswith(('.xlsx', '.xls')):
                df = parse_excel(contents)
            elif filename.endswith('.csv'):
                df = parse_csv(contents)
            elif filename.endswith('.CH2'):
                content_type, content_string = contents.split(',')
                decoded = base64.b64decode(content_string)
                with tempfile.NamedTemporaryFile(suffix='.CH2', delete=False) as temp_file:
                    temp_file.write(decoded)
                    temp_path = temp_file.name
                df = read_CH2(temp_path)
                os.unlink(temp_path)
            elif filename.endswith('.cdf'):
                content_type, content_string = contents.split(',')
                decoded = base64.b64decode(content_string)
                with tempfile.NamedTemporaryFile(suffix='.cdf', delete=False) as temp_file:
                    temp_file.write(decoded)
                    temp_path = temp_file.name
                df = read_cdf(temp_path)
                os.unlink(temp_path)
            else:
                print("Invalid file format")
                return f"Error: Invalid file format. Supported formats are .xlsx, .xls, .csv, .CH2, and .cdf", dash.no_update, dash.no_update, dash.no_update, dash.no_update

            if df is None or df.empty:
                print("Empty dataframe")
                return "Error: Empty file or file could not be parsed", dash.no_update, dash.no_update, dash.no_update, dash.no_update

            if len(df.columns) < 2:
                print("Missing columns")
                return "Error: File must contain time and signal columns", dash.no_update, dash.no_update, dash.no_update, dash.no_update

            formatted_data = {
                "time_steps": df.iloc[:, 0].tolist(),
                "signal": df.iloc[:, 1].tolist(),
                "columns": ["Time (min)", filename]
            }
            print(f"Sample legend set to: {filename}")
            print(f"Sample data points: {len(formatted_data['time_steps'])}")

            return "", formatted_data, None, "", None

        except Exception as e:
            print(f"Error in process_sample_upload: {e}")
            return f"Error processing file: {str(e)}", dash.no_update, dash.no_update, dash.no_update, dash.no_update

    @app.callback(
        [Output('upload-status', 'children', allow_duplicate=True),
         Output('stored-background-data', 'data', allow_duplicate=True)],
        [Input('upload-background', 'contents')],
        [State('upload-background', 'filename')],
        prevent_initial_call=True
    )
    def handle_uploads(background_contents, background_filename):
        ctx = dash.callback_context
        trigger = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else None

        status_message = ""
        background_data = dash.no_update
        if trigger == 'upload-background' and background_contents:
            try:
                background_df = None

                if background_filename.endswith(('.xlsx', '.xls')):
                    background_df = parse_excel(background_contents)
                elif background_filename.endswith('.csv'):
                    background_df = parse_csv(background_contents)
                elif background_filename.endswith('.CH2'):
                    content_type, content_string = background_contents.split(',')
                    decoded = base64.b64decode(content_string)
                    with tempfile.NamedTemporaryFile(suffix='.CH2', delete=False) as temp_file:
                        temp_file.write(decoded)
                        temp_path = temp_file.name
                    background_df = read_CH2(temp_path)
                    os.unlink(temp_path)
                elif background_filename.endswith('.cdf'):
                    content_type, content_string = background_contents.split(',')
                    decoded = base64.b64decode(content_string)
                    with tempfile.NamedTemporaryFile(suffix='.cdf', delete=False) as temp_file:
                        temp_file.write(decoded)
                        temp_path = temp_file.name
                    background_df = read_cdf(temp_path)
                    os.unlink(temp_path)
                else:
                    return "Error: Invalid file format for background", dash.no_update

                if background_df is not None:
                    bg_name = os.path.splitext(background_filename)[0]
                    background_data = {
                        "time_steps": background_df.iloc[:, 0].tolist(),
                        "signal": background_df.iloc[:, 1].tolist(),
                        "columns": ["Time (min)", bg_name]
                    }
                    print(f"Background legend set to: {bg_name}")
                    return "", background_data
                else:
                    return f"Error: Could not parse background file {background_filename}", dash.no_update
            except Exception as e:
                print(f"Error loading background: {e}")
                return str(e), dash.no_update

        return "", dash.no_update 

    @app.callback(
        [Output('peaks-api-status', 'children'),
         Output('api-result-store', 'data'),
         Output('stored-params-data', 'data', allow_duplicate=True),
         Output('stored-events-data', 'data', allow_duplicate=True)],
        Input('refresh-peaks-button', 'n_clicks'),
        [State('stored-sample-data', 'data'),
         State('stored-params-data', 'data'),
         State('stored-events-data', 'data')],
        prevent_initial_call=True
    )
    def refresh_peaks_from_api(n_clicks, sample_data, params_data, events_data):
        if not n_clicks or not sample_data:
            return "Error: No sample data", dash.no_update, dash.no_update, dash.no_update

        try:
            # Sample data should already be in correct format from process_sample_upload
            if not isinstance(sample_data, dict) or 'time_steps' not in sample_data or 'signal' not in sample_data:
                return "Error: Invalid sample data format", dash.no_update, dash.no_update, dash.no_update

            # Transform integration parameters if needed
            integration_params = {}
            if params_data:
                # Map the parameters from UI format to API format
                if isinstance(params_data, list) and len(params_data) > 0:
                    params = params_data[0]  # Get first row if it's a list
                else:
                    params = params_data

                integration_params = {
                    "peak_width": float(params.get("PeakWidth", 0)),
                    "detection_threshold": float(params.get("DetectionThreshold", 0)),
                    "liftoff": float(params.get("LiftoffPct", 0)) / 100,  # Convert from percentage
                    "touchdown": float(params.get("TouchdownPct", 0)) / 100,  # Convert from percentage
                    "min_area": float(params.get("MinimumArea", 0)),
                    "min_height": float(params.get("MinimumHeight", 0)),  # Add minimum height parameter
                    "integration_start": float(params.get("IntegrationStart", 0)),
                    "integration_end": float(params.get("IntegrationEnd", 0))
                }

            # First call the API to get the results - now returns (result, payload)
            result, api_payload = process_peaktable_api(
                chromatogram_data=sample_data,
                integration_params=integration_params,
                integration_events=events_data or []
            )

            if result is None:
                return "Error: API request failed", dash.no_update, dash.no_update, dash.no_update

            # Check if parameters were modified during validation
            updated_params = dash.no_update
            updated_events = dash.no_update

            if isinstance(result, dict) and "_modified_params" in result:
                modified = result["_modified_params"]

                # Update integration_end in parameters if it was modified
                if "integration_end" in modified and params_data:
                    if isinstance(params_data, list) and len(params_data) > 0:
                        # Create a deep copy to avoid modifying the original
                        params_copy = [dict(item) for item in params_data]
                        params_copy[0]["IntegrationEnd"] = float(modified["integration_end"])
                        updated_params = params_copy
                    else:
                        # Ensure we're working with a proper dictionary
                        updated_params = dict(params_data) if params_data else {}
                        updated_params["IntegrationEnd"] = float(modified["integration_end"])
                        # Always return a list to maintain consistent format
                        updated_params = [updated_params]

                # Update events if they were filtered
                if "filtered_events" in modified and modified["filtered_events"] is not None:
                    updated_events = modified["filtered_events"]

            # Process the result to ensure it's in the correct format for the UI
            if isinstance(result, dict) and "peaks" in result:
                # Use the peaks list directly - this is the format expected by the UI
                ui_ready_result = result["peaks"]
            elif isinstance(result, list):
                # Already a list of peaks, use as is
                ui_ready_result = result
            else:
                # Unexpected format - convert to empty list to avoid errors
                logger.error(f"Unexpected result format: {type(result)}")
                ui_ready_result = []

            # Save to database AFTER UI data is prepared
            try:
                # Wrap peaks list in a dictionary for database storage
                db_result = {"peaks": ui_ready_result}
                peaktable_id = save_peaktable_to_db(
                    payload=api_payload,
                    result=db_result,  # Save the wrapped peaks list
                    request_submitter="ui_user"
                )
            except Exception as e:
                logger.error(f"Database error saving peaktable: {str(e)}")
                peaktable_id = None

            # Create success message
            num_peaks = len(ui_ready_result)
            success_message = f"Successfully processed {num_peaks} peaks"

            # Add Peak Table ID to the success message if available
            if peaktable_id:
                success_message = f"{success_message} (ID: {peaktable_id})"

                # Augment the result with peaktable_id for client-side tracking
                ui_result_with_id = {"peaktable_id": peaktable_id, "data": ui_ready_result}
                return success_message, ui_result_with_id, updated_params, updated_events
            else:
                # No peaktable_id, just return the result
                return success_message, ui_ready_result, updated_params, updated_events

        except Exception as e:
            print(f"Error refreshing peaks: {e}")
            import traceback
            traceback.print_exc()
            return f"Error: {str(e)}", dash.no_update, dash.no_update, dash.no_update 

    # --- Migrated callbacks from archive/callbacks copy.py for full home page feature support ---
    # (All callbacks below this line are required for the UI in home_page.py)
    
    # Save zoom state
    @app.callback(
        Output('graph-zoom-state', 'data'),
        [Input('chromatogram-plot', 'relayoutData')],
        [State('graph-zoom-state', 'data')],
        prevent_initial_call=True
    )
    def save_zoom_state(relayoutData, current_state):
        if relayoutData:
            if 'xaxis.range[0]' in relayoutData and 'xaxis.range[1]' in relayoutData:
                return relayoutData
            elif 'xaxis.autorange' in relayoutData and relayoutData['xaxis.autorange'] and current_state:
                return current_state
            elif current_state and any(k.startswith('xaxis') or k.startswith('yaxis') for k in relayoutData):
                updated_state = dict(current_state)
                updated_state.update(relayoutData)
                return updated_state
        return current_state if current_state else dash.no_update

    # Handle table edits and deletions without changing page
    @app.callback(
        [Output('stored-peaks-data', 'data', allow_duplicate=True),
         Output('chromatogram-plot', 'figure', allow_duplicate=True)],
        [Input('peaks-data-table', 'data')],
        [State('chromatogram-plot', 'figure'),
         State('stored-sample-data', 'data'),
         State('stored-background-data', 'data'),
         State('upload-sample', 'filename'),
         State('upload-background', 'filename')],
        prevent_initial_call=True
    )
    def handle_peaks_table_changes(stored_data, current_figure, sample_data, background_data, sample_filename, background_filename):
        if stored_data:
            updated_data = []
            for row in stored_data:
                new_row = {k: v for k, v in row.items() if k not in ['Row']}
                updated_data.append(new_row)
            
            # Return None instead of an empty list when all peaks are deleted
            # This ensures the peak traces are properly cleared
            if len(updated_data) == 0:
                # When all rows are deleted, we need to explicitly update the figure to remove all peak traces
                
                # Get current zoom state
                current_x_range = None
                current_y_range = None
                if 'layout' in current_figure:
                    if 'xaxis' in current_figure['layout'] and 'range' in current_figure['layout']['xaxis']:
                        current_x_range = current_figure['layout']['xaxis']['range']
                    if 'yaxis' in current_figure['layout'] and 'range' in current_figure['layout']['yaxis']:
                        current_y_range = current_figure['layout']['yaxis']['range']
                
                # Recreate sample and background dataframes
                sample_df = None
                if sample_data:
                    sample_df = pd.DataFrame({
                        "time_steps": sample_data["time_steps"],
                        "signal": sample_data["signal"]
                    })
                    if "columns" in sample_data and len(sample_data["columns"]) >= 2:
                        sample_df.columns = sample_data["columns"]
                    else:
                        sample_df.columns = ["Time (min)", "Sample"]
                        
                background_df = None
                if background_data:
                    background_df = pd.DataFrame({
                        "time_steps": background_data["time_steps"],
                        "signal": background_data["signal"]
                    })
                    if "columns" in background_data and len(background_data["columns"]) >= 2:
                        background_df.columns = background_data["columns"]
                    else:
                        background_df.columns = ["Time (min)", "Background"]
                
                # Create new figure with no peaks
                use_sample_filename = (sample_filename and sample_df is not None and
                                    not sample_df.columns[1].startswith('Channel'))
                use_bg_filename = (background_filename and background_df is not None and
                                not background_df.columns[1].startswith('Channel'))
                
                fig = create_figure(
                    sample_df,
                    background_df,
                    None,  # No peaks
                    reset=False,
                    sample_filename=sample_filename if use_sample_filename else None,
                    background_filename=background_filename if use_bg_filename else None
                )
                
                # Preserve the current zoom state
                if current_x_range and current_y_range:
                    fig['layout']['xaxis']['range'] = current_x_range
                    fig['layout']['xaxis']['autorange'] = False
                    fig['layout']['yaxis']['range'] = current_y_range
                    fig['layout']['yaxis']['autorange'] = False
                
                # Ensure drag mode supports box zoom
                fig.update_layout(dragmode='select')
                
                return None, fig
                
            return updated_data, dash.no_update
        return dash.no_update, dash.no_update

    # Update peaks from API response
    @app.callback(
        [Output('peaks-table-container', 'children', allow_duplicate=True),
         Output('stored-peaks-data', 'data', allow_duplicate=True)],
        Input('api-result-store', 'data'),
        prevent_initial_call=True
    )
    def update_peaks_from_api_response(api_result):
        if not api_result:
            return dash.no_update, dash.no_update
        try:
            peaks_data = []
            if isinstance(api_result, dict) and 'data' in api_result:
                nested_result = api_result['data']
                if isinstance(nested_result, list):
                    peaks_data = nested_result
                elif isinstance(nested_result, dict) and 'peaks' in nested_result:
                    peaks_data = nested_result['peaks']
            elif isinstance(api_result, list):
                peaks_data = api_result
            elif isinstance(api_result, dict):
                if 'peaks' in api_result:
                    peaks_data = api_result['peaks']
                elif any(key in api_result for key in ['Name', 'RetentionTime', 'StartTime']):
                    peaks_data = [api_result]
            normalized_peaks = []
            for peak in peaks_data:
                peak_entry = {
                    "Name": peak.get("Name", peak.get("name", "Unknown")),
                    "IntType": peak.get("IntType", peak.get("int_type", "--")),
                    "RetentionTime": float(peak.get("RetentionTime", peak.get("retention_time", 0.0))),
                    "StartTime": float(peak.get("StartTime", peak.get("start_time", 0.0))),
                    "EndTime": float(peak.get("EndTime", peak.get("end_time", 0.0))),
                    "Slope": float(peak.get("Slope", peak.get("slope"))) if peak.get("Slope") is not None or peak.get("slope") is not None else "",
                    "Offset": float(peak.get("Offset", peak.get("offset"))) if peak.get("Offset") is not None or peak.get("offset") is not None else ""
                }
                normalized_peaks.append(peak_entry)
            normalized_peaks = sorted(normalized_peaks, key=lambda x: x.get('StartTime', 0))
            table = dash_table.DataTable(
                id='peaks-data-table',
                columns=[
                    {"name": "Peak #", "id": "Row", "deletable": False, "editable": False},
                    {"name": "Name", "id": "Name", "type": "text", "deletable": False, "editable": True},
                    {"name": "Integration Type", "id": "IntType", "type": "text", "deletable": False, "editable": True},
                    {"name": "Retention Time (min)", "id": "RetentionTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                    {"name": "Start Time", "id": "StartTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                    {"name": "End Time", "id": "EndTime", "type": "numeric", "format": {"specifier": ".4f"}, "deletable": False, "editable": True},
                    {"name": "Slope", "id": "Slope", "type": "numeric", "format": {"specifier": ".8f"}, "deletable": False, "editable": True},
                    {"name": "Offset", "id": "Offset", "type": "numeric", "format": {"specifier": ".8f"}, "deletable": False, "editable": True},
                    {"name": "Comment", "id": "Comment", "type": "text", "deletable": False, "editable": True},
                ],
                data=[{**peak, "Row": i+1} for i, peak in enumerate(normalized_peaks)],
                editable=True,
                row_deletable=True,
                style_table={'width': '100%'},
                style_cell={
                    'textAlign': 'left',
                    'padding': '8px',
                    'minWidth': '80px',
                    'width': '120px',
                    'maxWidth': '180px',
                    'whiteSpace': 'normal',
                    'height': 'auto',
                },
                style_header={
                    'backgroundColor': 'rgb(230, 230, 230)',
                    'fontWeight': 'bold'
                },
                style_data_conditional=[
                    {'if': {'row_index': 'odd'}, 'backgroundColor': 'rgb(248, 248, 248)'}
                ],
                page_size=10,
                page_action='native',
                page_current=0
            )
            return table, normalized_peaks
        except Exception as e:
            import traceback
            traceback.print_exc()
            return dash.no_update, dash.no_update

    # Update peaks table display when peaks data changes
    @app.callback(
        [Output('peaks-table-container', 'children', allow_duplicate=True),
         Output('peaks-api-status', 'children', allow_duplicate=True)],
        Input('stored-peaks-data', 'data'),
        prevent_initial_call=True
    )
    def update_peaks_display(peaks_data):
        if peaks_data is None:
            return html.Div("No peaks data available"), ""
        return dash.no_update, dash.no_update

    # Feedback save status
    @app.callback(
        Output('feedback-save-status', 'children'),
        Input('peaks-data-table', 'data'),
        [State('api-result-store', 'data'),
         State('peaks-data-table', 'data_previous')],
        prevent_initial_call=True
    )
    def save_peaks_feedback(current_data, api_result, previous_data):
        if not current_data or current_data == previous_data:
            return dash.no_update
        try:
            peaktable_id = None
            if isinstance(api_result, dict):
                peaktable_id = api_result.get("peaktable_id")
            if not peaktable_id:
                logger.warning("No peaktable_id found, cannot save feedback")
                return dash.no_update
            peak_data = []
            for peak in current_data:
                clean_peak = {k: v for k, v in peak.items() if k != "Row"}
                peak_data.append(clean_peak)
            operation_type = "table_edit"
            if previous_data and len(current_data) > len(previous_data):
                operation_type = "peak_added"
            elif previous_data and len(current_data) < len(previous_data):
                operation_type = "peak_deleted"
            else:
                for curr, prev in zip(current_data, previous_data):
                    if curr.get('Comment', '') != prev.get('Comment', ''):
                        operation_type = "comment_edited"
                        break
            success = save_peaktable_feedback(peaktable_id, peak_data, operation_type)
            return "Feedback saved" if success else "Error saving feedback"
        except Exception as e:
            logger.error(f"Error saving peak table feedback: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return f"Error: {str(e)}"

    @app.callback(
        Output('feedback-save-status', 'children', allow_duplicate=True),
        [Input('add-row-button', 'n_clicks'),
         Input('save-peak-button', 'n_clicks')],
        [State('peaks-data-table', 'data'),
         State('api-result-store', 'data')],
        prevent_initial_call=True
    )
    def save_row_changes(add_clicks, save_clicks, current_data, api_result):
        if not current_data:
            return dash.no_update
        ctx = dash.callback_context
        if not ctx.triggered:
            return dash.no_update
        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
        if trigger_id not in ['add-row-button', 'save-peak-button']:
            return dash.no_update
        try:
            peaktable_id = None
            if isinstance(api_result, dict):
                peaktable_id = api_result.get("peaktable_id")
            if not peaktable_id:
                logger.warning("No peaktable_id found, cannot save row changes")
                return dash.no_update
            peak_data = []
            for peak in current_data:
                clean_peak = {k: v for k, v in peak.items() if k != "Row"}
                peak_data.append(clean_peak)
            operation_type = "row_added" if trigger_id == 'save-peak-button' else "row_operation"
            success = save_peaktable_feedback(peaktable_id, peak_data, operation_type)
            return "Row changes saved" if success else "Error saving row changes"
        except Exception as e:
            logger.error(f"Error saving row changes: {e}")
            return f"Error: {str(e)}"

    # Table page state
    @app.callback(
        Output('table-page-state', 'data'),
        [Input('peaks-data-table', 'page_current')],
        prevent_initial_call=True
    )
    def store_current_page(current_page):
        return {'current_page': current_page}

    # Direct box zoom
    @app.callback(
        Output('chromatogram-plot', 'figure', allow_duplicate=True),
        [Input('chromatogram-plot', 'selectedData')],
        [State('chromatogram-plot', 'figure')],
        prevent_initial_call=True
    )
    def direct_zoom(selectedData, figure):
        if not selectedData or 'range' not in selectedData:
            return dash.no_update
        x_range = selectedData['range']['x']
        y_range = selectedData['range']['y']
        figure['layout']['xaxis']['range'] = x_range
        figure['layout']['yaxis']['range'] = y_range
        figure['layout']['xaxis']['autorange'] = False
        figure['layout']['yaxis']['autorange'] = False
        return figure

    # Store selection data
    @app.callback(
        Output('selected-data', 'children'),
        [Input('chromatogram-plot', 'selectedData')],
        prevent_initial_call=True
    )
    def store_selection(selectedData):
        if selectedData:
            return json.dumps(selectedData)
        return dash.no_update

    # Error modal close
    @app.callback(
        Output('error-modal', 'style', allow_duplicate=True),
        [Input('close-error-button', 'n_clicks')],
        prevent_initial_call=True
    )
    def close_error_modal(n_clicks):
        return {'display': 'none'}

    # Download callbacks
    @app.callback(
        Output("download-params-csv", "data"),
        [Input("download-params-btn", "n_clicks")],
        [State("minAreaSlider", "value"),
         State("minHeightSlider", "value"),
         State("integrationStartSlider", "value"),
         State("integrationEndSlider", "value"),
         State("peakWidthSlider", "value"),
         State("detectionThresholdSlider", "value"),
         State("liftoffSlider", "value"),
         State("touchdownSlider", "value")],
        prevent_initial_call=True
    )
    def download_params_csv(n_clicks, min_area, min_height, int_start, int_end,
                          peak_width, detection, liftoff, touchdown):
        params_dict = {
            'MinimumArea': f"{float(min_area):.2f}",
            'MinimumHeight': f"{float(min_height):.2f}",
            'IntegrationStart': f"{float(int_start):.2f}",
            'IntegrationEnd': f"{float(int_end):.2f}",
            'PeakWidth': f"{float(peak_width):.2f}",
            'DetectionThreshold': f"{float(detection):.2f}",
            'LiftoffPct': f"{float(liftoff):.2f}",
            'TouchdownPct': f"{float(touchdown):.2f}"
        }
        df = pd.DataFrame([params_dict])
        import datetime
        filename = f"Integration_parameters_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return dcc.send_data_frame(df.to_csv, filename, index=False)

    @app.callback(
        Output("download-events-csv", "data"),
        [Input("download-events-btn", "n_clicks")],
        [State("stored-events-data", "data")],
        prevent_initial_call=True
    )
    def download_events_csv(n_clicks, stored_data):
        if not stored_data:
            return dash.no_update
        df = pd.DataFrame(stored_data)
        df = df.fillna('')
        if 'Value' in df.columns:
            df['Value'] = df['Value'].apply(lambda x: '{:.2f}'.format(float(x)) if x != '' else '')
        if 'Stop' in df.columns:
            df['Stop'] = df['Stop'].apply(lambda x: '{:.3f}'.format(float(x)) if x != '' else '')
        if 'Time' in df.columns:
            df['Time'] = df['Time'].apply(lambda x: '{:.3f}'.format(float(x)) if x != '' else '')
        import datetime
        filename = f"Integration_events_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return dcc.send_data_frame(df.to_csv, filename, index=False, na_rep='')

    @app.callback(
        Output("download-peaks-csv", "data"),
        [Input("download-button", "n_clicks")],
        [State("stored-peaks-data", "data")],
        prevent_initial_call=True
    )
    def download_peaks_csv(n_clicks, stored_data):
        if not stored_data:
            return dash.no_update
        df = pd.DataFrame(stored_data)
        import datetime
        filename = f"Peak_table_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return dcc.send_data_frame(df.to_csv, filename, index=False)

    # Demo file downloads
    @app.callback(
        Output("download-demo-peaks", "data"),
        Input("download-demo-peaks-btn", "n_clicks"),
        prevent_initial_call=True
    )
    def download_demo_peaks(n_clicks):
        return dcc.send_file("templates/peak_table_demo.xlsx")

    @app.callback(
        Output("download-demo-params", "data"),
        Input("download-demo-params-btn", "n_clicks"),
        prevent_initial_call=True
    )
    def download_demo_params(n_clicks):
        return dcc.send_file("templates/integration_parameters_demo.xlsx")

    @app.callback(
        Output("download-demo-events", "data"),
        Input("download-demo-events-btn", "n_clicks"),
        prevent_initial_call=True
    )
    def download_demo_events(n_clicks):
        return dcc.send_file("templates/integration_events_demo.xlsx")

    # Parameter slider/input sync and update
    # (Add all parameter slider/input sync callbacks here, as in archive/callbacks copy.py)
    # ... (Omitted for brevity, but all such callbacks should be migrated as well)

    # --- Continued migration: Parameter slider/input sync and integration events table callbacks ---

    # Parameter slider to input field binding
    @app.callback(
        Output('minArea', 'value'),
        [Input('minAreaSlider', 'value')],
        prevent_initial_call=True
    )
    def update_min_area_input(value):
        return f"{value:.2f}"

    @app.callback(
        Output('minHeight', 'value'),
        [Input('minHeightSlider', 'value')],
        prevent_initial_call=True
    )
    def update_min_height_input(value):
        return f"{value:.2f}"

    @app.callback(
        Output('integrationStart', 'value'),
        [Input('integrationStartSlider', 'value')],
        prevent_initial_call=True
    )
    def update_integration_start_input(value):
        value = max(value, MIN_START)
        return f"{value:.2f}"

    @app.callback(
        Output('integrationEnd', 'value'),
        [Input('integrationEndSlider', 'value')],
        prevent_initial_call=True
    )
    def update_integration_end_input(value):
        return f"{value:.2f}"

    @app.callback(
        Output('peakWidth', 'value'),
        [Input('peakWidthSlider', 'value')],
        prevent_initial_call=True
    )
    def update_peak_width_input(value):
        return f"{value:.2f}"

    @app.callback(
        Output('detectionThreshold', 'value'),
        [Input('detectionThresholdSlider', 'value')],
        prevent_initial_call=True
    )
    def update_detection_threshold_input(value):
        return f"{value:.4f}"

    @app.callback(
        Output('liftoff', 'value'),
        [Input('liftoffSlider', 'value')],
        prevent_initial_call=True
    )
    def update_liftoff_input(value):
        return f"{value:.2f}"

    @app.callback(
        Output('touchdown', 'value'),
        [Input('touchdownSlider', 'value')],
        prevent_initial_call=True
    )
    def update_touchdown_input(value):
        return f"{value:.2f}"

    # Input field to slider binding (reverse)
    @app.callback(
        Output('minAreaSlider', 'value', allow_duplicate=True),
        [Input('minArea', 'n_submit'), Input('minArea', 'n_blur')],
        [State('minArea', 'value')],
        prevent_initial_call=True
    )
    def update_min_area_slider(n_submit, n_blur, value):
        if value:
            try:
                return float(value)
            except:
                return dash.no_update
        return dash.no_update

    @app.callback(
        Output('minHeightSlider', 'value', allow_duplicate=True),
        [Input('minHeight', 'n_submit'), Input('minHeight', 'n_blur')],
        [State('minHeight', 'value')],
        prevent_initial_call=True
    )
    def update_min_height_slider(n_submit, n_blur, value):
        if value:
            try:
                return float(value)
            except:
                return dash.no_update
        return dash.no_update

    @app.callback(
        Output('integrationStartSlider', 'value', allow_duplicate=True),
        [Input('integrationStart', 'n_submit'), Input('integrationStart', 'n_blur')],
        [State('integrationStart', 'value')],
        prevent_initial_call=True
    )
    def update_integration_start_slider(n_submit, n_blur, value):
        if value:
            try:
                return max(float(value), MIN_START)
            except:
                return dash.no_update
        return dash.no_update

    @app.callback(
        Output('integrationEndSlider', 'value', allow_duplicate=True),
        [Input('integrationEnd', 'n_submit'), Input('integrationEnd', 'n_blur')],
        [State('integrationEnd', 'value')],
        prevent_initial_call=True
    )
    def update_integration_end_slider(n_submit, n_blur, value):
        if value:
            try:
                return float(value)
            except:
                return dash.no_update
        return dash.no_update

    @app.callback(
        Output('peakWidthSlider', 'value', allow_duplicate=True),
        [Input('peakWidth', 'n_submit'), Input('peakWidth', 'n_blur')],
        [State('peakWidth', 'value')],
        prevent_initial_call=True
    )
    def update_peak_width_slider(n_submit, n_blur, value):
        if value:
            try:
                return float(value)
            except:
                return dash.no_update
        return dash.no_update

    @app.callback(
        Output('detectionThresholdSlider', 'value', allow_duplicate=True),
        [Input('detectionThreshold', 'n_submit'), Input('detectionThreshold', 'n_blur')],
        [State('detectionThreshold', 'value')],
        prevent_initial_call=True
    )
    def update_detection_threshold_slider(n_submit, n_blur, value):
        if value:
            try:
                return float(value)
            except:
                return dash.no_update
        return dash.no_update

    @app.callback(
        Output('liftoffSlider', 'value', allow_duplicate=True),
        [Input('liftoff', 'n_submit'), Input('liftoff', 'n_blur')],
        [State('liftoff', 'value')],
        prevent_initial_call=True
    )
    def update_liftoff_slider(n_submit, n_blur, value):
        if value:
            try:
                return float(value)
            except:
                return dash.no_update
        return dash.no_update

    @app.callback(
        Output('touchdownSlider', 'value', allow_duplicate=True),
        [Input('touchdown', 'n_submit'), Input('touchdown', 'n_blur')],
        [State('touchdown', 'value')],
        prevent_initial_call=True
    )
    def update_touchdown_slider(n_submit, n_blur, value):
        if value:
            try:
                return float(value)
            except:
                return dash.no_update
        return dash.no_update

    # --- Integration Events Table and Modal Callbacks ---
    @app.callback(
        Output('add-event-modal', 'style'),
        [Input('add-event-btn', 'n_clicks'),
         Input('cancel-event-button', 'n_clicks'),
         Input('save-event-button', 'n_clicks')],
        [State('add-event-modal', 'style')],
        prevent_initial_call=True
    )
    def toggle_event_modal(add_clicks, cancel_clicks, save_clicks, style):
        ctx = dash.callback_context
        if not ctx.triggered:
            return style
        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
        if trigger_id == 'add-event-btn':
            new_style = dict(style)
            new_style['display'] = 'block'
            return new_style
        elif trigger_id in ['cancel-event-button', 'save-event-button']:
            new_style = dict(style)
            new_style['display'] = 'none'
            return new_style
        return style

    @app.callback(
        [Output('event-type-input', 'options'),
         Output('event-type-input', 'value')],
        Input('add-event-btn', 'n_clicks'),
        prevent_initial_call=True
    )
    def populate_event_type_dropdown(n_clicks):
        options = [{'label': k, 'value': k} for k, v in event_type_options.items()]
        return options, None

    @app.callback(
        [Output('event-form-error', 'children'),
         Output('save-event-button', 'disabled')],
        [Input('event-time-input', 'value'),
         Input('event-type-input', 'value'),
         Input('event-value-input', 'value')],
        prevent_initial_call=True
    )
    def validate_event_form(time_value, type_value, event_value):
        if time_value is None or not type_value:
            return "Time and Type are required", True
        try:
            time_value = round(float(time_value), 3)
        except (ValueError, TypeError):
            return "Time must be a number", True
        value_required_types = event_type_options.keys()
        if type_value != "Detect Shoulders" and type_value != "Valley to Valley" and type_value in value_required_types and event_value is None:
            return f"Value is required for {type_value}", True
        return "", False

    @app.callback(
        Output('events-data-table', 'data'),
        [Input('stored-events-data', 'data')],
        prevent_initial_call=True
    )
    def update_events_table(stored_data):
        if stored_data:
            return stored_data
        return []

    @app.callback(
        [Output('stored-events-data', 'data', allow_duplicate=True),
         Output('upload-events', 'contents')],
        [Input('events-data-table', 'data'),
         Input('upload-events', 'contents'),
         Input('save-event-button', 'n_clicks')],
        [State('upload-events', 'filename'),
         State('event-time-input', 'value'),
         State('event-type-input', 'value'),
         State('event-value-input', 'value'),
         State('event-stop-input', 'value'),
         State('stored-events-data', 'data')],
        prevent_initial_call=True
    )
    def update_events_data(table_data, contents, save_clicks,
                          filename, event_time, event_type, event_value, event_stop,
                          stored_data):
        ctx = dash.callback_context
        trigger = ctx.triggered[0]['prop_id'].split('.')[0]
        if trigger == 'events-data-table':
            if not table_data:
                return dash.no_update, dash.no_update
            try:
                df = pd.DataFrame(table_data)
                if 'Time' not in df.columns or 'Type' not in df.columns:
                    return dash.no_update, dash.no_update
                df['Time'] = pd.to_numeric(df['Time']).round(3)
                if 'Value' in df.columns:
                    df['Value'] = pd.to_numeric(df['Value'], errors='coerce')
                if 'Stop' in df.columns:
                    df['Stop'] = pd.to_numeric(df['Stop'], errors='coerce').round(3)
                df = df.sort_values(by='Time')
                return df.to_dict('records'), None
            except (ValueError, TypeError):
                return dash.no_update, dash.no_update
        elif trigger == 'upload-events':
            if contents is None:
                return dash.no_update, dash.no_update
            try:
                if filename and filename.endswith(('.xlsx', '.xls')):
                    df = parse_excel(contents)
                elif filename and filename.endswith('.csv'):
                    df = parse_csv(contents)
                else:
                    return dash.no_update, dash.no_update
                if df is None or df.empty or 'Time' not in df.columns or 'Type' not in df.columns:
                    return dash.no_update, dash.no_update
                df['Time'] = pd.to_numeric(df['Time']).round(3)
                if 'Value' in df.columns:
                    df['Value'] = pd.to_numeric(df['Value'], errors='coerce')
                if 'Stop' in df.columns:
                    df['Stop'] = pd.to_numeric(df['Stop'], errors='coerce').round(3)
                df = df.sort_values(by='Time')
                return df.to_dict('records'), None
            except Exception as e:
                return dash.no_update, dash.no_update
        elif trigger == 'save-event-button':
            if not save_clicks or event_time is None or not event_type:
                return dash.no_update, dash.no_update
            new_event = {
                "Time": round(float(event_time), 3),
                "Type": event_type,
                "Value": float(event_value) if event_value is not None else None,
                "Stop": round(float(event_stop), 3) if event_stop is not None else None
            }
            if stored_data is None:
                stored_data = []
            updated_data = stored_data + [new_event]
            updated_data = sorted(updated_data, key=lambda x: x['Time'])
            return updated_data, dash.no_update 

    # --- Minimal revision: Add missing callback for Upload Global Parameters (upload-params) ---
    @app.callback(
        [Output('minAreaSlider', 'value', allow_duplicate=True),
         Output('minHeightSlider', 'value', allow_duplicate=True),
         Output('integrationStartSlider', 'value', allow_duplicate=True),
         Output('integrationEndSlider', 'value', allow_duplicate=True),
         Output('peakWidthSlider', 'value', allow_duplicate=True),
         Output('detectionThresholdSlider', 'value', allow_duplicate=True),
         Output('liftoffSlider', 'value', allow_duplicate=True),
         Output('touchdownSlider', 'value', allow_duplicate=True),
         Output('stored-params-data', 'data', allow_duplicate=True),
         Output('error-modal', 'style', allow_duplicate=True),
         Output('error-message', 'children', allow_duplicate=True),
         Output('upload-params', 'contents')],
        [Input('upload-params', 'contents')],
        [State('upload-params', 'filename'),
         State('stored-params-data', 'data')],
        prevent_initial_call=True
    )
    def update_parameters(contents, filename, current_params):
        ctx = dash.callback_context
        if not ctx.triggered or ctx.triggered[0]['prop_id'].split('.')[0] != 'upload-params':
            return [dash.no_update] * 12
        if contents is None:
            return [dash.no_update] * 12
        df = None
        if filename and filename.endswith(('.xlsx', '.xls')):
            df = parse_excel(contents)
        elif filename and filename.endswith('.csv'):
            df = parse_csv(contents)
        else:
            return [dash.no_update] * 8 + [dash.no_update, {'display': 'block'}, "Error: Invalid file format. Use .xlsx, .xls, or .csv", None]
        if df is None or df.empty:
            return [dash.no_update] * 8 + [dash.no_update, {'display': 'block'}, "Error: Could not parse file or file is empty.", None]
        required_params = {
            'MinimumArea': 'Minimum Area',
            'MinimumHeight': 'Minimum Height',
            'IntegrationStart': 'Integration Start',
            'IntegrationEnd': 'Integration End',
            'PeakWidth': 'Peak Width',
            'DetectionThreshold': 'Detection Threshold',
            'LiftoffPct': 'Liftoff Percentage',
            'TouchdownPct': 'Touchdown Percentage'
        }
        missing_params = [required_params[col] for col in required_params if col not in df.columns]
        if missing_params:
            error_msg = f"Error: Missing required parameters: {', '.join(missing_params)}"
            return [dash.no_update] * 8 + [dash.no_update, {'display': 'block'}, error_msg, None]
        try:
            params = df.iloc[0]
            min_area = float(params['MinimumArea'])
            min_height = float(params['MinimumHeight'])
            int_start = float(params['IntegrationStart'])
            int_end = float(params['IntegrationEnd'])
            peak_width = float(params['PeakWidth'])
            detection = float(params['DetectionThreshold'])
            liftoff = float(params['LiftoffPct'])
            touchdown = float(params['TouchdownPct'])
            stored_data = df.iloc[[0]].to_dict('records')
            return min_area, min_height, int_start, int_end, peak_width, detection, liftoff, touchdown, stored_data, {'display': 'none'}, "", None
        except (ValueError, TypeError) as e:
            error_msg = f"Error: Invalid numeric values in parameters file: {str(e)}"
            return [dash.no_update] * 8 + [dash.no_update, {'display': 'block'}, error_msg, None]

    # --- Minimal fix: Add callback to toggle Add Peak modal ---
    @app.callback(
        Output('add-peak-modal', 'style'),
        [Input('add-row-button', 'n_clicks'),
         Input('cancel-peak-button', 'n_clicks'),
         Input('save-peak-button', 'n_clicks')],
        [State('add-peak-modal', 'style')],
        prevent_initial_call=True
    )
    def toggle_peak_modal(add_clicks, cancel_clicks, save_clicks, style):
        ctx = dash.callback_context
        if not ctx.triggered:
            return style
        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
        if trigger_id == 'add-row-button':
            return {'display': 'block'}
        elif trigger_id in ['cancel-peak-button', 'save-peak-button']:
            return {'display': 'none'}
        return style

    # --- Minimal fix: Store parameter changes to stored-params-data on slider/input change ---
    @app.callback(
        Output('stored-params-data', 'data', allow_duplicate=True),
        [
            Input('minAreaSlider', 'value'),
            Input('minHeightSlider', 'value'),
            Input('integrationStartSlider', 'value'),
            Input('integrationEndSlider', 'value'),
            Input('peakWidthSlider', 'value'),
            Input('detectionThresholdSlider', 'value'),
            Input('liftoffSlider', 'value'),
            Input('touchdownSlider', 'value'),
            Input('minArea', 'n_submit'), Input('minArea', 'n_blur'),
            Input('minHeight', 'n_submit'), Input('minHeight', 'n_blur'),
            Input('integrationStart', 'n_submit'), Input('integrationStart', 'n_blur'),
            Input('integrationEnd', 'n_submit'), Input('integrationEnd', 'n_blur'),
            Input('peakWidth', 'n_submit'), Input('peakWidth', 'n_blur'),
            Input('detectionThreshold', 'n_submit'), Input('detectionThreshold', 'n_blur'),
            Input('liftoff', 'n_submit'), Input('liftoff', 'n_blur'),
            Input('touchdown', 'n_submit'), Input('touchdown', 'n_blur'),
        ],
        [
            State('minArea', 'value'),
            State('minHeight', 'value'),
            State('integrationStart', 'value'),
            State('integrationEnd', 'value'),
            State('peakWidth', 'value'),
            State('detectionThreshold', 'value'),
            State('liftoff', 'value'),
            State('touchdown', 'value'),
        ],
        prevent_initial_call=True
    )
    def update_stored_params(*args):
        # The last 8 args are the State values (input box values)
        input_values = args[-8:]
        # The first 8 args are the slider values
        slider_values = args[:8]
        ctx = dash.callback_context
        trigger = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else None
        # If the trigger is an input box, use input values; otherwise, use slider values
        param_names = [
            'MinimumArea', 'MinimumHeight', 'IntegrationStart', 'IntegrationEnd',
            'PeakWidth', 'DetectionThreshold', 'LiftoffPct', 'TouchdownPct'
        ]
        if trigger in ['minArea', 'minHeight', 'integrationStart', 'integrationEnd',
                       'peakWidth', 'detectionThreshold', 'liftoff', 'touchdown']:
            # Use input values (convert to float, default to 0 if invalid)
            params = {}
            for name, val in zip(param_names, input_values):
                try:
                    params[name] = float(val) if val is not None and val != '' else 0
                except Exception:
                    params[name] = 0
        else:
            # Use slider values
            params = {name: float(val) if val is not None else 0 for name, val in zip(param_names, slider_values)}
        return [params]

    # --- Minimal fix: Add callback for Add Peak modal form validation ---
    @app.callback(
        [Output('peak-form-error', 'children'),
         Output('save-peak-button', 'disabled')],
        [Input('peak-start-input', 'value'),
         Input('peak-end-input', 'value')]
    )
    def validate_peak_form(start_time, end_time):
        if start_time is None or end_time is None:
            return "Start Time and End Time are required", True
        return "", False