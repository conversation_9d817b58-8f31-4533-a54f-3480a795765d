trigger:
  - master
  - release/*
  - release
  - dev

pool: 'devops-agent'

variables:
  project_name: chromassist
  image_name: harbor.di.wuxiapptec.com/dcs/${SYSTEM_TEAMPROJECT,,}/$(project_name)
  image_tag: ${BUILD_SOURCEVERSION:0:7}
  image_path: $(image_name):$(image_tag)
  branch: ${BUILD_SOURCEBRANCHNAME,,}
  pr_image: $IMAGE_NAME:pr-$SYSTEM_PULLREQUEST_PULLREQUESTID
  kustomize_path: ${SYSTEM_TEAMPROJECT,,}/env/$(branch)

steps:
  - checkout: self
    clean: true
    submodules: true
  - script: |
      echo build编号='$(Build.BuildNumber)'
      echo $(image_path)
      env
      docker build -t $(image_path) .
      docker push $(image_path)
    displayName: 'Build image and pust image'

  - script: |
      git clone ssh://azuredevops.wuxiapptec.com:22/DI_Collection/GitOps/_git/projects deployment
      cd deployment/$(kustomize_path)
      kustomize edit set image $(image_name)=$(image_path)
      git commit -am "[$(kustomize_path)] bump ${BUILD_REPOSITORY_NAME,,} image version to $(image_path)"
      git push || (git pull && git push)
    condition: and(succeeded(), in(variables['Build.SourceBranch'], 'refs/heads/master', 'refs/heads/release','refs/heads/dev'))
    displayName: 'update dev env'