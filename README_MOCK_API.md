# Mock Identify Peaks API

This document describes the mock API server for the "Identify Peaks" functionality in the predict page.

## Overview

The mock API simulates a real peak identification service that would typically run on a separate server. It provides realistic peak detection using scipy's signal processing capabilities.

## Files

- `mock_identify_peaks_api.py` - The main mock API server
- `start_mock_api.py` - Helper script to start the server
- `README_MOCK_API.md` - This documentation

## Starting the Mock API Server

### Method 1: Direct execution
```bash
python mock_identify_peaks_api.py
```

### Method 2: Using the helper script
```bash
python start_mock_api.py
```

The server will start on `http://localhost:8001` and provide the following endpoints:

- `POST /identify-peaks` - Main API endpoint for peak identification
- `GET /health` - Health check endpoint
- `GET /` - API information and available endpoints

## API Usage

### Request Format

Send a POST request to `/identify-peaks` with the following JSON payload:

```json
{
    "signal": [array of signal values],
    "time_steps": [array of time values],
    "integration_start": 0.0,
    "integration_end": 10.0,
    "baselines": [
        {"start": 2.0, "end": 3.0},
        {"start": 7.0, "end": 8.0}
    ]
}
```

### Response Format

Successful response:
```json list

    [
        {
            "Name": "Peak_1",
            "IntType": "BB",
            "RetentionTime": 5.23,
            "StartTime": 5.10,
            "EndTime": 5.36,
            "Slope": 0.0012,
            "Offset": 1234.5
        },
    ]
```

Error response:
```
    []
```

## Peak Detection Algorithm

The mock API uses scipy's `find_peaks` function with the following parameters:

- **Minimum distance**: Calculated as `len(signal) // 50` to prevent too many close peaks
- **Minimum height**: Mean signal + 0.5 * standard deviation
- **Prominence**: 0.3 * standard deviation of the signal

### Baseline Handling

- Peaks detected within baseline regions are automatically excluded
- Baselines must have both start and end times to be considered valid
- Baseline regions are clipped to the integration range

### Peak Boundaries

For each detected peak, the algorithm:
1. Finds the peak maximum (retention time)
2. Searches left and right for valley points (start/end times)
3. Calculates a linear baseline slope and offset

## Integration with Main Application

The main application calls this API through the `process_identify_peaks_api()` function in `utils.py`. The function:

1. Validates input data
2. Processes baseline information
3. Makes an HTTP POST request to the mock API
4. Returns the peak list and original payload

## Development Notes

- The API saves request/response data to the `temp/` directory for debugging
- All requests are logged with timestamps
- The server runs in debug mode for development
- Connection timeout is set to 30 seconds

## Error Handling

The API handles various error conditions:

- Missing required fields
- Invalid data formats
- Mismatched array lengths
- Invalid integration ranges
- Peak detection failures

## Dependencies

- Flask 3.0.0
- NumPy 1.26.2
- SciPy 1.11.4

## Testing

You can test the API using curl:

```bash
# Health check
curl http://localhost:8001/health

# API info
curl http://localhost:8001/

# Test peak identification (with sample data)
curl -X POST http://localhost:8001/identify-peaks \
  -H "Content-Type: application/json" \
  -d '{
    "signal": [100, 150, 200, 180, 120, 300, 250, 100],
    "time_steps": [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0],
    "integration_start": 1.0,
    "integration_end": 8.0,
    "baselines": []
  }'
``` 