/* General styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
}

.app-title {
    text-align: center;
    color: #333;
    margin-top: 0;
    margin-bottom: 20px;
}

/* File uploads section */
.file-uploads {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.upload-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 10px;
}

.upload-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;  /* Added to align label and box */
    width: 100%;
}

.upload-item label {
    font-weight: bold;
    margin-bottom: 5px;
    margin-left: 0%;  /* Added to align with upload box */
}

/* Container for upload box and download button */
.upload-with-download {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 5px;
}

.upload-box {
    flex-grow: 1;
    height: 40px;
    width: 200px;
    line-height: 40px;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px;
    text-align: left;  /* Changed from center to left */
    padding-left: 15px;  /* Added left padding */
    margin: 10px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f8f8;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.upload-box:hover {
    border-color: #2196F3;
    background-color: #e8f4fd;  /* Slightly blue background on hover */
}

/* Upload and download icon buttons */
.upload-icon-btn,
.download-icon-btn {
    height: 40px;
    width: 40px;
    border-radius: 5px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    transition: all 0.3s ease;
    font-size: 20px;
    padding: 0;
    line-height: 1;
}

.upload-icon-btn:hover,
.download-icon-btn:hover {
    color: #2196F3;
}

/* Main content layout */
.main-content {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 20px;
}

@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
    }
}

.left-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.side-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Panel styles */
.panel {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.parameters-card {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.panel-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.card-header h2 {
    font-size: 1.3rem;
    margin: 0;
    padding-bottom: 0;
    border-bottom: none;
    color: #333;
}

/* Plot container */
.plot-container {
    width: 100%;
    height: 450px;
}

/* Button styles */
.buttons {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.action-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin: 0 5px;
}

.action-button:hover {
    background-color: #45a049;
}

.download-btn {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.download-btn:hover {
    background-color: #0b7dda;
}

/* Peak Table Header */
.peak-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.peak-table-buttons {
    display: flex;
    gap: 10px;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

th, td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    font-size: 14px;
}

th {
    background-color: #f2f2f2;
}

.table-container {
    max-height: 600px;
    overflow-y: auto;
}

/* Parameter controls */
.parameter-group {
    margin-bottom: 15px;
}

.parameter-row {
    display: flex;
    align-items: center;  /* Added to vertically center items */
    margin-bottom: 12px;  /* Increased spacing between rows */
    font-size: 14px;
}

.parameter-name {
    font-weight: bold;
    width: 150px;  /* Fixed width instead of flex-basis */
    text-align: right;  /* Right-align parameter names */
    padding-right: 10px;  /* Add spacing before the slider */
}

.parameter-value {
    display: flex;
    align-items: center;
    gap: 12px;  /* Increased gap between slider, input and unit */
    flex-grow: 1;  /* Take up remaining space */
}

.parameter-slider {
    width: 120px;  /* Slightly wider slider */
    cursor: pointer;
    height: 8px !important;  /* Make slider twice as thick */
}

/* Custom slider styling */
.rc-slider-track {
    background-color: #2196F3 !important;
    height: 8px !important;  /* Double thickness */
}

.rc-slider-rail {
    background-color: #e9e9e9 !important;
    height: 8px !important;  /* Double thickness */
}

.rc-slider-handle {
    border-color: #2196F3 !important;
    margin-top: -6px !important;  /* Adjust for thicker slider */
    width: 16px !important;  /* Larger handle */
    height: 16px !important;  /* Larger handle */
}

.rc-slider-mark {
    display: none !important; /* Hide all ticks */
}

.parameter-input {
    width: 60px !important; /* Slightly wider input */
    text-align: right !important;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.3s;
}

.parameter-input:focus {
    border-color: #2196F3;
    outline: none;
    box-shadow: 0 0 3px rgba(33, 150, 243, 0.3);
}

.unit {
    color: #666;
    font-size: 12px;
    width: 50px;
}

/* Events table */
.events-table-container {
    max-height: 800px;
    overflow-y: auto;
    margin-top: 10px;
}

.add-event-btn {
    background-color: #4CAF50;  /* Changed to green to match action buttons */
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    /* Remove the display: block and margin: auto properties */
}

.add-event-btn:hover {
    background-color: #45a049;  /* Add hover effect to match other buttons */
}

/* Add this class if it doesn't exist */
.events-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.events-table-buttons {
    display: flex;
    gap: 10px;
}

.edit-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 4px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

/* Modal styles */
.modal {
    z-index: 2000 !important; /* Ensure it's above other elements */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.7) !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    transition: opacity 0.3s ease;
}

.modal[style*="display: none"] {
    display: none !important;
    opacity: 0 !important;
}

.modal[style*="display: block"] {
    opacity: 1 !important;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    width: 80%;
    max-width: 600px;
    position: relative;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    align-items: center;
}

.modal-content h2,
.modal-content .error-message,
.modal-content .modal-buttons {
    grid-column: span 2;
}

.modal-content label {
    font-weight: bold;
    text-align: right;
    padding-right: 10px;
}

.modal-content input,
.modal-content .Select {
    width: 100%;
    box-sizing: border-box; /* Ensures padding doesn't affect final width */
}

/* Ensure specific inputs inside the modal have consistent styling */
.modal-input {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border-radius: 4px;
    grid-column: span 2; /* Make error span both columns */
}

/* Enhanced error styling for API status messages */
.status-message {
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    font-weight: 500;
}

/* Error status styling */
.status-message[style*="color: red"],
.status-message:contains("Error") {
    background-color: #ffebee;
    border: 1px solid #f44336;
    color: #d32f2f;
}

/* Success status styling */
.status-message[style*="color: green"] {
    background-color: #e8f5e8;
    border: 1px solid #4caf50;
    color: #2e7d32;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
}

.modal-button {
    padding: 10px;
    margin: 0 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.modal-button.primary {
    background-color: #007bff;
    color: white;
}

.modal-button.primary:hover {
    background-color: #0056b3;
}

/* Loading Spinner */
.loading-spinner {
    border: 4px solid #f3f3f3; /* Light grey */
    border-top: 4px solid #3498db; /* Blue */
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Make modal responsive */
@media (max-width: 600px) {
    .modal-content {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .modal-content h2,
    .modal-content .error-message,
    .modal-content .modal-buttons,
    .modal-content label {
        grid-column: span 1;
        text-align: left;
    }
}

/* Dash-specific styles */
.dash-spreadsheet-container {
    max-width: 100%;
    overflow-x: auto;
}

.dash-spreadsheet {
    font-family: Arial, sans-serif;
}

.dash-spreadsheet-inner td, .dash-spreadsheet-inner th {
    font-size: 14px;
    padding: 8px;
}

/* Custom file upload styling for Dash */
.dash-upload {
    border: 1px dashed #ccc;
    border-radius: 5px;
    text-align: center;
    padding: 20px;
    cursor: pointer;
}

.dash-upload:hover {
    border-color: #aaa;
    background-color: #f9f9f9;
}

/* Direct DOM manipulation for modal transitions */
#cloud-loading-modal[style*="display: block"] {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 3000 !important;
    animation: fadeIn 0.2s ease-in-out !important;
}

#cloud-upload-modal[style*="display: block"] {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 3000 !important;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Force visible modal content */
#cloud-loading-modal .modal-content {
    background-color: white !important;
    border-radius: 5px !important;
    padding: 20px !important;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.7) !important;
    width: 90% !important;
    max-width: 500px !important;
    margin: 0 auto !important;
    position: relative !important;
    z-index: 9999 !important;
    opacity: 1 !important;
}

.force-hide {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    z-index: -1 !important;
}

/* Add more explicit modal closing rules */
#cloud-loading-modal[style*="display: none"],
.modal.force-hide {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Additional hard overrides for modal behavior */
#cloud-upload-modal {
    transition: none !important;
}

#cloud-loading-modal {
    transition: none !important;
}

/* Ensure cloud-upload-modal is always visible when display is block */
#cloud-upload-modal[style*="display: block"] {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 9999 !important;
}

/* Ensure cloud-loading-modal is never visible unless explicitly shown */
#cloud-loading-modal {
    display: none !important;
}

/* Only when explicitly set to display:block, show the loading modal */
#cloud-loading-modal[style*="display: block"] {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 9999 !important;
}

/* Identify Peaks button - responsive styling */
.identify-peaks-btn {
    background: #2196F3;
    color: white;
    width: 90%;
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 4px;
    margin-top: 8px;
    margin-left: 5%;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
    position: relative;
    overflow: hidden;
}

.identify-peaks-btn:hover {
    background: #1976D2;
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
    transform: translateY(-1px);
}

.identify-peaks-btn:active {
    background: #1565C0;
    box-shadow: 0 1px 2px rgba(33, 150, 243, 0.3);
    transform: translateY(0);
}

.identify-peaks-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

.identify-peaks-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

/* Ripple effect for click feedback */
.identify-peaks-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.identify-peaks-btn:active::before {
    width: 100px;
    height: 100px;
}

/* Loading state for Identify Peaks button */
.identify-peaks-btn.loading {
    background: #1976D2;
    cursor: wait;
    position: relative;
}

.identify-peaks-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: button-spin 1s linear infinite;
}

.identify-peaks-btn.loading span {
    opacity: 0;
}

@keyframes button-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}