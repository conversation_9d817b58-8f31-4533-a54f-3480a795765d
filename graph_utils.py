import plotly.graph_objects as go
import pandas as pd
import os

def create_figure(sample_df, background_df, peaks_df=None, baseline_lines=None, reset=False, sample_filename=None, background_filename=None, selected_peak_index=None):
    """
    Create a plotly figure from sample data, background data, and peaks data

    Parameters:
    -----------
    sample_df : pandas DataFrame or dict
        DataFrame containing sample data with time in first column and signal in second
        OR dictionary with 'time_steps', 'signal', and 'columns' keys
    background_df : pandas DataFrame or dict
        DataFrame containing background data with time in first column and signal in second
        OR dictionary with 'time_steps', 'signal', and 'columns' keys
    peaks_df : pandas DataFrame
        DataFrame containing peak data with StartTime, EndTime, etc.
    baseline_lines : dict
        Dictionary containing baseline line data with index as key and coordinates as values
    reset : bool
        Whether to reset zoom/pan state
    sample_filename : str
        Name of the sample file
    background_filename : str
        Name of the background file
    selected_peak_index : int
        Index of the selected peak to highlight

    Returns:
    --------
    fig : plotly Figure
        Plotly figure object
    """
    fig = go.Figure()

    # Convert dictionary format to DataFrame if needed
    sample_dataframe = None
    if sample_df is not None:
        if isinstance(sample_df, dict) and 'time_steps' in sample_df and 'signal' in sample_df:
            # Convert dictionary to DataFrame
            if 'columns' in sample_df and len(sample_df['columns']) >= 2:
                column_names = sample_df['columns']
            else:
                column_names = ["Time (min)", "Sample"]
            
            sample_dataframe = pd.DataFrame({
                column_names[0]: sample_df['time_steps'],
                column_names[1]: sample_df['signal']
            })
        else:
            # Already a DataFrame
            sample_dataframe = sample_df

    background_dataframe = None
    if background_df is not None:
        if isinstance(background_df, dict) and 'time_steps' in background_df and 'signal' in background_df:
            # Convert dictionary to DataFrame
            if 'columns' in background_df and len(background_df['columns']) >= 2:
                column_names = background_df['columns']
            else:
                column_names = ["Time (min)", "Background"]
            
            background_dataframe = pd.DataFrame({
                column_names[0]: background_df['time_steps'],
                column_names[1]: background_df['signal']
            })
        else:
            # Already a DataFrame
            background_dataframe = background_df

    # Add sample trace if available
    if sample_dataframe is not None and len(sample_dataframe.columns) >= 2:
        time_col = sample_dataframe.columns[0]
        signal_col = sample_dataframe.columns[1]

        # Use signal column name for SMB data, filename for file uploads
        legend_name = signal_col if signal_col.startswith("Channel") else (
            os.path.splitext(sample_filename)[0] if sample_filename else signal_col)

        fig.add_trace(
            go.Scatter(
                x=sample_dataframe[time_col],
                y=sample_dataframe[signal_col],
                mode='lines',
                name=legend_name,
                line=dict(color='blue', width=1),
                showlegend=True  # Explicitly set showlegend to True
            )
        )

        # Add peak start/end lines if both sample and peaks data are available
        if peaks_df is not None and len(peaks_df) > 0:
            add_peak_markers(fig, sample_dataframe, peaks_df, time_col, signal_col, selected_peak_index)

        # Add baseline lines if available
        if baseline_lines and isinstance(baseline_lines, dict):
            add_baseline_lines(fig, baseline_lines)

    # Add background trace if available
    if background_dataframe is not None and len(background_dataframe.columns) >= 2:
        time_col = background_dataframe.columns[0]
        signal_col = background_dataframe.columns[1]

        # Use signal column name for SMB data, filename for file uploads (same as sample)
        legend_name = signal_col if signal_col.startswith("Channel") else (
            os.path.splitext(background_filename)[0] if background_filename else signal_col)

        fig.add_trace(
            go.Scatter(
                x=background_dataframe[time_col],
                y=background_dataframe[signal_col],
                mode='lines',
                name=legend_name,
                line=dict(color='purple', width=1),
                showlegend=True  # Explicitly set showlegend to True
            )
        )

    # Always show legend even with single trace
    fig.update_layout(
        showlegend=True,  # Force legend to always show
        margin=dict(
            t=40,
            pad=4
        ),
        height=500,
        width=900,
        plot_bgcolor='rgba(240, 245, 250, 0.8)',
        paper_bgcolor='white',
        xaxis=dict(
            title='Time',
            showgrid=True,
            rangeslider=dict(visible=True),
            autorange=True if reset else None,
            fixedrange=False  # Allow x-axis zoom
        ),
        yaxis=dict(
            title='Signal Intensity',
            showgrid=True,
            autorange=True if reset else None,
            fixedrange=False  # Allow y-axis zoom
        ),
        dragmode='select',  # Set default drag mode to select for box select
        hovermode='closest',
        selectdirection='any',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="center",
            x=0.5
        ),
        clickmode='event+select'  # Enable all interactions
    )

    return fig

def add_peak_markers(fig, sample_df, peaks_df, time_col, signal_col, selected_peak_index=None):
    """
    Add peak markers and baselines to the figure

    Parameters:
    -----------
    fig : plotly Figure
        Figure to add markers to
    sample_df : pandas DataFrame
        Sample data
    peaks_df : pandas DataFrame
        Peak data
    time_col : str
        Name of time column
    signal_col : str
        Name of signal column
    selected_peak_index : int
        Index of the selected peak to highlight
    """
    # Helper function to find y-value in sample data at given x
    def get_sample_y_at_x(x_value):
        # Ensure sample_df is sorted by time
        sample_df_sorted = sample_df.sort_values(by=time_col)

        # Find the nearest points
        if x_value <= sample_df_sorted[time_col].min():
            # If x is before first point, return first y
            return sample_df_sorted[signal_col].iloc[0]
        elif x_value >= sample_df_sorted[time_col].max():
            # If x is after last point, return last y
            return sample_df_sorted[signal_col].iloc[-1]
        else:
            # Find points before and after x
            lower_idx = sample_df_sorted[sample_df_sorted[time_col] <= x_value][time_col].idxmax()
            upper_idx = sample_df_sorted[sample_df_sorted[time_col] >= x_value][time_col].idxmin()

            # Get x and y values for interpolation
            x1 = sample_df_sorted.loc[lower_idx, time_col]
            y1 = sample_df_sorted.loc[lower_idx, signal_col]
            x2 = sample_df_sorted.loc[upper_idx, time_col]
            y2 = sample_df_sorted.loc[upper_idx, signal_col]

            # Linear interpolation
            if x1 == x2:  # Same point
                return y1
            else:
                return y1 + (y2 - y1) * (x_value - x1) / (x2 - x1)

    # Debug: Print peaks data
    print(f"\nPeaks DataFrame:\n{peaks_df}")
    print(f"Peaks DataFrame columns: {peaks_df.columns.tolist()}")
    print(f"Peaks DataFrame dtypes: {peaks_df.dtypes}")

    # For each peak, draw a red line from start to end time
    for i, row in peaks_df.iterrows():
        try:
            # Determine color based on selection
            peak_color = 'orange' if i == selected_peak_index else 'red'
            
            # Get values from the peaks data
            start_time = float(row['StartTime'])
            end_time = float(row['EndTime'])

            # # Debug: Print current peak data
            # print(f"\nProcessing peak {i+1}:")
            # print(f"  StartTime: {start_time}, EndTime: {end_time}")
            # if 'Slope' in row:
            #     print(f"  Slope: {row['Slope']} (type: {type(row['Slope'])})")
            # if 'Offset' in row:
            #     print(f"  Offset: {row['Offset']} (type: {type(row['Offset'])})")

            # Check if this is a peak with slope/offset information
            # More lenient check to handle different data types from API
            has_slope = False
            has_offset = False

            if 'Slope' in row and pd.notna(row['Slope']):
                try:
                    slope_val = float(row['Slope'])
                    has_slope = slope_val != 0
                    # print(f"  has_slope: {has_slope} (slope_val: {slope_val})")
                except (ValueError, TypeError) as e:
                    print(f"  Error converting Slope to float: {e}")
                    has_slope = row['Slope'] != ''
                    # print(f"  has_slope: {has_slope} (using string check)")
            else:
                print("  Slope column not found or value is NaN")

            if 'Offset' in row and pd.notna(row['Offset']):
                try:
                    # Convert to float to check if it's a valid number
                    offset_val = float(row['Offset'])
                    has_offset = True  # Even zero offset is valid
                    # print(f"  has_offset: {has_offset} (offset_val: {offset_val})")
                except (ValueError, TypeError) as e:
                    print(f"  Error converting Offset to float: {e}")
                    has_offset = row['Offset'] != ''
                    # print(f"  has_offset: {has_offset} (using string check)")
            else:
                print("  Offset column not found or value is NaN")

            has_int_type = 'IntType' in row and pd.notna(row['IntType']) and row['IntType'] != ''
            # print(f"  has_int_type: {has_int_type}")

            has_complete_info = has_slope and has_offset
            # print(f"  has_complete_info: {has_complete_info}")

            # Get name if available
            peak_name = f"Peak {i}"
            if 'Name' in row and row['Name']:
                peak_name = f"Peak: {row['Name']}"

            if has_complete_info:
                # Use method with linear equation for complete data
                try:
                    slope = float(row['Slope'])
                    offset = float(row['Offset'])
                except (ValueError, TypeError):
                    print(f"Error converting Slope/Offset to float: {row['Slope']}, {row['Offset']}")
                    # Fall back to the old method
                    has_complete_info = False

                if has_complete_info:  # Only calculate if we still have complete info
                    # Calculate y values using the linear equation: y = Slope*x + Offset
                    start_y = slope * 1 * start_time + offset
                    end_y = slope * 1 * end_time + offset
                    # print(f"  Calculated baseline: start_y={start_y}, end_y={end_y}")

                if has_complete_info:
                    # Add red line trace for baseline
                    fig.add_trace(
                        go.Scatter(
                            x=[start_time, end_time],
                            y=[start_y, end_y],
                            mode='lines',
                            name=peak_name,
                            line=dict(color=peak_color, width=1),
                            showlegend=False,
                            customdata=[i]
                        )
                    )

                    # Add triangle markers at start and end points on baseline
                    fig.add_trace(
                        go.Scatter(
                            x=[start_time],
                            y=[start_y],
                            mode='markers',
                            marker=dict(
                                symbol='triangle-up',
                                size=8,
                                color='rgba(0,0,0,0)',
                                line=dict(color=peak_color, width=1)
                            ),
                            name=f"{peak_name} Start",
                            showlegend=False,
                            customdata=[i]
                        )
                    )

                    fig.add_trace(
                        go.Scatter(
                            x=[end_time],
                            y=[end_y],
                            mode='markers',
                            marker=dict(
                                symbol='triangle-up',
                                size=8,
                                color='rgba(0,0,0,0)',
                                line=dict(color=peak_color, width=1)
                            ),
                            name=f"{peak_name} End",
                            showlegend=False,
                            customdata=[i]
                        )
                    )

                # Process IntType if needed
                if has_complete_info and has_int_type:  # Only process if we have complete info
                    int_type = str(row['IntType'])

                    # Skip if it's "Missing"
                    if int_type == "Missing":
                        continue

                    # Process if it's a 2-character string
                    if len(int_type) == 2:
                        # Process first character for start point
                        if int_type[0] != "B":
                            # Get sample signal y value at start_time
                            sample_y_at_start = get_sample_y_at_x(start_time)

                            # Check if vertical line can reach sample signal
                            if start_y < sample_y_at_start:
                                # Add vertical line from start point to sample signal
                                fig.add_trace(
                                    go.Scatter(
                                        x=[start_time, start_time],
                                        y=[start_y, sample_y_at_start],
                                        mode='lines',
                                        line=dict(color=peak_color, width=1, dash='dot'),
                                        showlegend=False,
                                        customdata=[i]
                                    )
                                )

                        # Process second character for end point
                        if int_type[1] != "B":
                            # Get sample signal y value at end_time
                            sample_y_at_end = get_sample_y_at_x(end_time)

                            # Check if vertical line can reach sample signal
                            if end_y < sample_y_at_end:
                                # Add vertical line from end point to sample signal
                                fig.add_trace(
                                    go.Scatter(
                                        x=[end_time, end_time],
                                        y=[end_y, sample_y_at_end],
                                        mode='lines',
                                        line=dict(color=peak_color, width=1, dash='dot'),
                                        showlegend=False,
                                        customdata=[i]
                                    )
                                )
            else:
                # Use the old method for incomplete data
                # Ensure sample_df is sorted by time for finding closest points
                sample_df_sorted = sample_df.sort_values(by=time_col)

                # Find closest points in sample data
                start_idx = (sample_df_sorted[time_col] - start_time).abs().idxmin()
                end_idx = (sample_df_sorted[time_col] - end_time).abs().idxmin()

                start_point = sample_df_sorted.loc[start_idx]
                end_point = sample_df_sorted.loc[end_idx]

                # Add red line trace using sample data points
                fig.add_trace(
                    go.Scatter(
                        x=[start_point[time_col], end_point[time_col]],
                        y=[start_point[signal_col], end_point[signal_col]],
                        mode='lines',
                        name=peak_name,
                        line=dict(color=peak_color, width=1),
                        showlegend=False,
                        customdata=[i]
                    )
                )

                # Add triangle markers at start and end points
                fig.add_trace(
                    go.Scatter(
                        x=[start_point[time_col]],
                        y=[start_point[signal_col]],
                        mode='markers',
                        marker=dict(
                            symbol='triangle-up',
                            size=8,
                            color='rgba(0,0,0,0)',
                            line=dict(color=peak_color, width=1)
                        ),
                        name=f"{peak_name} Start",
                        showlegend=False,
                        customdata=[i]
                    )
                )

                fig.add_trace(
                    go.Scatter(
                        x=[end_point[time_col]],
                        y=[end_point[signal_col]],
                        mode='markers',
                        marker=dict(
                            symbol='triangle-up',
                            size=8,
                            color='rgba(0,0,0,0)',
                            line=dict(color=peak_color, width=1)
                        ),
                        name=f"{peak_name} End",
                        showlegend=False,
                        customdata=[i]
                    )
                )

        except (ValueError, KeyError, TypeError) as e:
            print(f"Error processing peak {i}: {e}")

def add_baseline_lines(fig, baseline_lines):
    """
    Add green baseline lines to the figure
    
    Parameters:
    -----------
    fig : plotly Figure
        Figure to add baseline lines to
    baseline_lines : dict
        Dictionary with baseline index as key and line data as values
        Each value should contain: start_coords, end_coords, slope, offset
    """
    for baseline_index, line_data in baseline_lines.items():
        try:
            start_coords = line_data.get('start_coords')
            end_coords = line_data.get('end_coords')
            
            if start_coords and end_coords:
                # Add green baseline line
                fig.add_trace(
                    go.Scatter(
                        x=[start_coords[0], end_coords[0]],
                        y=[start_coords[1], end_coords[1]],
                        mode='lines',
                        name=f'Baseline {int(baseline_index) + 1}',
                        line=dict(color='green', width=2),
                        showlegend=True,
                        hovertemplate=f'Baseline {int(baseline_index) + 1}<br>' +
                                    f'Start: ({start_coords[0]:.3f}, {start_coords[1]:.2f})<br>' +
                                    f'End: ({end_coords[0]:.3f}, {end_coords[1]:.2f})<br>' +
                                    f'Slope: {line_data.get("slope", 0):.6f}<br>' +
                                    f'Offset: {line_data.get("offset", 0):.6f}<extra></extra>'
                    )
                )
                
                # Add circle markers at start and end points
                fig.add_trace(
                    go.Scatter(
                        x=[start_coords[0]],
                        y=[start_coords[1]],
                        mode='markers',
                        marker=dict(
                            symbol='circle',
                            size=6,
                            color='green',
                            line=dict(color='darkgreen', width=1)
                        ),
                        name=f'Baseline {int(baseline_index) + 1} Start',
                        showlegend=False,
                        hovertemplate=f'Baseline {int(baseline_index) + 1} Start<br>' +
                                    f'({start_coords[0]:.3f}, {start_coords[1]:.2f})<extra></extra>'
                    )
                )
                
                fig.add_trace(
                    go.Scatter(
                        x=[end_coords[0]],
                        y=[end_coords[1]],
                        mode='markers',
                        marker=dict(
                            symbol='circle',
                            size=6,
                            color='green',
                            line=dict(color='darkgreen', width=1)
                        ),
                        name=f'Baseline {int(baseline_index) + 1} End',
                        showlegend=False,
                        hovertemplate=f'Baseline {int(baseline_index) + 1} End<br>' +
                                    f'({end_coords[0]:.3f}, {end_coords[1]:.2f})<extra></extra>'
                    )
                )
                
        except Exception as e:
            print(f"Error adding baseline line {baseline_index}: {e}")

def generate_sample_figure():
    """
    Generate an empty figure to show on initial load

    Returns:
    --------
    fig : plotly Figure
        Empty plotly figure object with appropriate layout
    """
    fig = go.Figure()

    # Just create an empty figure with proper layout
    fig.update_layout(
        xaxis=dict(
            title='Time (min)',
            fixedrange=False  # Allow x-axis zoom
        ),
        yaxis=dict(
            title='Response (μV)',
            fixedrange=False  # Allow y-axis zoom
        ),
        margin=dict(
            l=50,
            r=50,
            b=50,
            t=20,
            pad=4
        ),
        dragmode='select',  # Set default drag mode to select for box select
        hovermode='closest',
        plot_bgcolor='rgba(240, 245, 250, 0.8)',  # Light blue background
        paper_bgcolor='white',
        modebar=dict(
            remove=['lasso', 'select']  # Remove selection tools only, don't try to add default buttons
        )
    )

    return fig
