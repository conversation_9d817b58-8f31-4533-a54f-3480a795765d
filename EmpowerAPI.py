import requests
import json
from typing import Optional, Dict, List

class EmpowerAPIClient:
    def __init__(self, base_url: str = "http://10.28.137.16:8010"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def login_project(self, project_name: str, account: str, 
                      password: str, database_name: str) -> Optional[Dict]:
        endpoint = f"{self.base_url}/api/EmpowerAuthentication/loginproject"
        payload = {
            'ProjectName': project_name,
            'Account': account,
            'Password': password,
            'DataBaseName': database_name
        }
        
        try:
            response = self.session.post(endpoint, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Login error: {e}")
            return None
        
    def get_sample_injections(self, sample_name: str, xml_filename: str) -> Optional[Dict]:
        """
        Get sample injections data
        
        Args:
            sample_name (str): Sample name (e.g. "SPL-1")
            xml_filename (str): XML filename
            
        Returns:
            Optional[Dict]: Response data or None if error
        """
        endpoint = f"{self.base_url}/api/EmpowerProject/sampleinjections"
        
        payload = {
            "SelName": sample_name,
            "XmlFileName": xml_filename
        }
        
        try:
            response = self.session.post(endpoint, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Get sample injections error: {e}")
            return None
        
    def get_project_methods(self, project_id: int) -> Optional[Dict]:
        endpoint = f"{self.base_url}/api/EmpowerProject/methods"
        try:
            response = self.session.post(endpoint, json=project_id)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Get methods error: {e}")
            return None
        
    def get_method_info(self, method_name: str) -> Optional[Dict]:
        """
        Get detailed information for a specific method
        
        Args:
            method_name (str): Name of the method to query
            
        Returns:
            Optional[Dict]: Method information or None if error
        """
        endpoint = f"{self.base_url}/api/EmpowerProject/methodsInfo"
        
        payload = {
            "MethodName": method_name
        }
        
        try:
            response = self.session.post(endpoint, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Get method info error: {e}")
            return None
        
    def get_sample_results(self, sample_name: str, xml_filename: str) -> Optional[Dict]:
        """
        Get sample results data
        
        Args:
            sample_name (str): Sample name (e.g. "SPL")
            xml_filename (str): XML filename for results
            
        Returns:
            Optional[Dict]: Sample results or None if error
        """
        endpoint = f"{self.base_url}/api/EmpowerProject/sampleresults"
        
        payload = {
            "SelName": sample_name,
            "XmlFileName": xml_filename
        }
        
        try:
            response = self.session.post(endpoint, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Get sample results error: {e}")
            return None
    

    def get_sample_result_peaks(self, resultID: list[str], xml_filename: str) -> Optional[Dict]:
        """
        Get peak results for specified samples
        
        Args:
            sample_names (list[str]): List of sample names/IDs
            xml_filename (str): XML filename for peak results
            
        Returns:
            Optional[Dict]: Peak results data or None if error
        """
        endpoint = f"{self.base_url}/api/EmpowerProject/sampleResultPeaks"
        
        payload = {
            "SelName": resultID,
            "XmlFileName": xml_filename
        }
        
        try:
            response = self.session.post(endpoint, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Get sample result peaks error: {e}")
            return None

    def get_sample_channels(self, sample_name: str, xml_filename: str) -> Optional[Dict]:
        """
        Get channel data for a sample
        
        Args:
            sample_name (str): Sample name/ID
            xml_filename (str): XML filename for channel data
            
        Returns:
            Optional[Dict]: Channel data or None if error
        """
        endpoint = f"{self.base_url}/api/EmpowerProject/samplechannels"
        print(endpoint)
        
        payload = {
            "SelName": sample_name,
            "XmlFileName": xml_filename
        }
        
        try:
            response = self.session.post(endpoint, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Get sample channels error: {e}")
            return None

    def export_chrom_channel_2d(self, export_name: str, channel_ids: list[str], xml_filename: str) -> Optional[Dict]:
        """
        Export 2D chromatogram channel data
        
        Args:
            export_name (str): Name for the export
            channel_ids (list[str]): List of channel IDs to export
            xml_filename (str): XML filename for export data
            
        Returns:
            Optional[Dict]: Export response data or None if error
        """
        endpoint = f"{self.base_url}/api/EmpowerProject/chromExportChannel2D"
        
        payload = {
            "ExportName": export_name,
            "ChannelID": channel_ids,
            "XmlFileName": xml_filename
        }
        
        try:
            response = self.session.post(endpoint, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Export 2D chromatogram channel error: {e}")
            return None    

