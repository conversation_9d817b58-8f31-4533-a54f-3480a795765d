import dash
from dash import dcc, html, dash_table
import flask
import predict_page # Import the new page
import home_page # Import the new home page
from home_callbacks import register_home_callbacks
from predict_callbacks import register_predict_callbacks
# Import callbacks AFTER defining app and MIN_START
# from callbacks import * # Moved down
import importlib
import graph_utils
importlib.reload(graph_utils)

# Initialize the Dash app
app = dash.Dash(
    __name__,
    title='Calculate Peak Table',
    suppress_callback_exceptions=True,
    use_pages=True,  # Enable pages
    pages_folder=""
)

# Initialize the Flask server
server = app.server

# Add health check endpoint
@server.route('/health')
def health_check():
    return flask.Response(status=200)

# Define MIN_START constant here
MIN_START = 0.5

# Update INITIAL_PARAMS to explicitly use MIN_START
INITIAL_PARAMS = {
    "MinimumArea": 2289.0,
    "MinimumHeight": 10.0,
    "IntegrationStart": MIN_START,  # Use the constant here
    "IntegrationEnd": 48.0,
    "PeakWidth": 20.0,
    "DetectionThreshold": 20.0,
    "LiftoffPct": 5.0,
    "TouchdownPct": 0.0,
    # Assuming DetectShoulder is handled elsewhere or defaults to True if needed here
}

# Register the new page
dash.register_page("home", layout=home_page.layout, path="/")
dash.register_page("predict", layout=predict_page.layout, path="/detect")

# Define the app layout
app.layout = html.Div([
    html.Div([  # Main container for the entire view
        # Navigation Links
        html.Div([
            dcc.Link("Calculate Peak Table", href=dash.page_registry['home']['path']),
            dcc.Link("Detect Peaks", href=dash.page_registry['predict']['path'], style={"marginLeft": "20px"}),
        ], className="navigation-bar", style={"padding": "10px", "backgroundColor": "#f0f0f0", "marginBottom": "20px"}),

        # Content of the current page will be rendered here
        dash.page_container
    ], className="container") # Apply 'container' class to wrap both nav and page content
])

# Register callbacks
register_home_callbacks(app)
register_predict_callbacks(app)

# Main entry point
if __name__ == '__main__':
    app.run_server(debug=True, host='0.0.0.0', port=8050)
