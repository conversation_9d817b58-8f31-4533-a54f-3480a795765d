FROM harbor.di.wuxiapptec.com/public/python:3.11-slim
# FROM python:3.11-slim

WORKDIR /app

# Install system dependencies required for netCDF4
RUN apt-get update && apt-get install -y \
    build-essential \
    libhdf5-dev \
    libnetcdf-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application
COPY . .

# Expose the port the app runs on
EXPOSE 8050

# Command to run the application
CMD ["gunicorn", "--bind", "0.0.0.0:8050", "app:server"]
